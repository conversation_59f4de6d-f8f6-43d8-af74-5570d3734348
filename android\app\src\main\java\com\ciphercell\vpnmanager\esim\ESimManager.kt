package com.ciphercell.vpnmanager.esim

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.telephony.SubscriptionInfo
import android.telephony.SubscriptionManager
import android.telephony.euicc.*
import androidx.annotation.RequiresApi
import androidx.annotation.RequiresPermission
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ESimManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val subscriptionManager: SubscriptionManager,
    private val euiccManager: EuiccManager
) {
    companion object {
        private const val TAG = "ESimManager"
        private const val ACTION_DOWNLOAD_SUBSCRIPTION = "com.ciphercell.vpnmanager.ACTION_DOWNLOAD_SUBSCRIPTION"
    }

    val isESimSupported: Boolean
        get() = euiccManager.isEnabled

    suspend fun getActiveESimProfiles(): List<SubscriptionInfo> = withContext(Dispatchers.IO) {
        if (!isESimSupported) return@withContext emptyList()
        
        subscriptionManager.availableSubscriptionInfoList?.filter { info ->
            info.isEmbedded
        } ?: emptyList()
    }

    @RequiresPermission(android.Manifest.permission.WRITE_EMBEDDED_SUBSCRIPTIONS)
    suspend fun downloadESimProfile(
        activationCode: String,
        confirmationCode: String = "",
        forceDeactivateSim: Boolean = false
    ): Boolean = withContext(Dispatchers.IO) {
        if (!isESimSupported) return@withContext false

        val callback = object : EuiccManager.DownloadableSubscriptionCallback() {
            override fun onComplete(result: DownloadableSubscription) {
                // Handle successful download
            }
        }

        try {
            val subscription = DownloadableSubscription.forActivationCode(activationCode)
            val intent = Intent(ACTION_DOWNLOAD_SUBSCRIPTION).apply {
                setPackage(context.packageName)
            }
            
            val callbackIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
            )
            
            euiccManager.downloadSubscription(
                subscription,
                forceDeactivateSim,
                null,  // resolvedBundle
                context.mainExecutor,
                callback
            )
            
            true
        } catch (e: Exception) {
            // Handle error
            false
        }
    }

    @RequiresPermission(android.Manifest.permission.WRITE_EMBEDDED_SUBSCRIPTIONS)
    suspend fun deleteESimProfile(iccid: String): Boolean = withContext(Dispatchers.IO) {
        if (!isESimSupported) return@withContext false
        
        try {
            euiccManager.deleteSubscription(
                euiccManager.createForCardId(0),  // cardId
                iccid,
                PendingIntent.getBroadcast(
                    context,
                    0,
                    Intent(context, ESimDeletionResultReceiver::class.java),
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
                )
            )
            true
        } catch (e: Exception) {
            // Handle error
            false
        }
    }

    @RequiresApi(28)
    suspend fun switchToESimProfile(iccid: String): Boolean = withContext(Dispatchers.IO) {
        if (!isESimSupported) return@withContext false
        
        try {
            subscriptionManager.switchToSubscription(iccid.toInt())
            true
        } catch (e: Exception) {
            // Handle error
            false
        }
    }

    fun getESimStatus(): ESimStatus {
        return if (!isESimSupported) {
            ESimStatus.NotSupported
        } else if (subscriptionManager.availableSubscriptionInfoList?.any { it.isEmbedded } == true) {
            ESimStatus.Active
        } else {
            ESimStatus.Inactive
        }
    }
}

sealed class ESimStatus {
    object NotSupported : ESimStatus()
    object Active : ESimStatus()
    object Inactive : ESimStatus()
}

class ESimDeletionResultReceiver : android.content.BroadcastReceiver() {
    override fun onReceive(context: android.content.Context, intent: android.content.Intent) {
        if (EuiccManager.ACTION_DOWNLOAD_SUBSCRIPTION.equals(intent.action)) {
            val resultCode = getResultCode()
            when (resultCode) {
                EuiccManager.EMBEDDED_SUBSCRIPTION_RESULT_OK -> {
                    // Handle success
                }
                else -> {
                    // Handle error
                }
            }
        }
    }
}
