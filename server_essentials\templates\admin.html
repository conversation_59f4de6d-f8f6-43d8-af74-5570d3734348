<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="CipherCell VPN Admin Panel - Manage CiphercellVPN backend services">
    <meta name="keywords" content="CiphercellVPN, admin, eSIM, VPN, management, dashboard">
    <meta name="author" content="CipherCell VPN">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CipherCell Admin">
    <title>Admin Panel - CipherCell VPN</title>
    <link rel="manifest" href="/static/manifest.json">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="apple-touch-icon" href="/static/icon-192x192.png">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            z-index: 1000;
        }

        .sidebar-header {
            text-align: center;
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            list-style: none;
            padding: 20px 0;
        }

        .sidebar-menu li {
            margin: 5px 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
        }

        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            margin-left: 250px;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: white;
            padding: 20px 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 2rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #667eea;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .content-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
        }

        .section-content {
            padding: 30px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .chart-container {
            height: 300px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-shield-alt"></i> CipherCell</h2>
            <p>Admin Panel</p>
        </div>
        <ul class="sidebar-menu">
            <li><a href="#" class="menu-link active" data-section="dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
            <li><a href="#" class="menu-link" data-section="users"><i class="fas fa-users"></i> Users</a></li>
            <li><a href="#" class="menu-link" data-section="subscriptions"><i class="fas fa-credit-card"></i> Subscriptions</a></li>
            <li><a href="#" class="menu-link" data-section="vpn"><i class="fas fa-network-wired"></i> VPN Status</a></li>
            <li><a href="#" class="menu-link" data-section="system"><i class="fas fa-server"></i> System</a></li>
            <li><a href="#" class="menu-link" data-section="logs"><i class="fas fa-file-alt"></i> Logs</a></li>
            <li><a href="#" class="menu-link" data-section="settings"><i class="fas fa-cog"></i> Settings</a></li>
            <li><a href="/"><i class="fas fa-home"></i> Back to Site</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="header">
            <h1>Admin Dashboard</h1>
            <div class="user-info">
                <span>Welcome, Admin</span>
                <i class="fas fa-user-circle" style="font-size: 1.5rem;"></i>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div class="content-section active" id="dashboard">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-sim-card"></i></div>
                    <div class="stat-number" id="activeESIMs">0</div>
                    <div class="stat-label">Active eSIMs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-vpn-card"></i></div>
                    <div class="stat-number" id="vpnConnections">0</div>
                    <div class="stat-label">VPN Connections</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                    <div class="stat-number" id="monthlyRevenue">$0</div>
                    <div class="stat-label">Monthly Revenue</div>
                </div>
            </div>

            <div class="content-section active">
                <div class="section-header">
                    <h2 class="section-title">System Overview</h2>
                    <button class="btn" onclick="refreshDashboard()"><i class="fas fa-sync"></i> Refresh</button>
                </div>
                <div class="section-content">
                    <div class="alert alert-info">
                        <strong>System Status:</strong> All services are running normally.
                    </div>
                    <div class="chart-container">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Section -->
        <div class="content-section" id="users">
            <div class="section-header">
                <h2 class="section-title">User Management</h2>
                <button class="btn" onclick="loadUsers()"><i class="fas fa-sync"></i> Refresh</button>
            </div>
            <div class="section-content">
                <table class="table" id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>IMSI</th>
                            <th>MSISDN</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Subscriptions Section -->
        <div class="content-section" id="subscriptions">
            <div class="section-header">
                <h2 class="section-title">Subscription Management</h2>
                <button class="btn" onclick="loadSubscriptions()"><i class="fas fa-sync"></i> Refresh</button>
            </div>
            <div class="section-content">
                <table class="table" id="subscriptionsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Plan</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Data Usage</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Subscriptions will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- VPN Section -->
        <div class="content-section" id="vpn">
            <div class="section-header">
                <h2 class="section-title">VPN Status</h2>
                <button class="btn" onclick="loadVPNStatus()"><i class="fas fa-sync"></i> Refresh</button>
            </div>
            <div class="section-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-server"></i></div>
                        <div class="stat-number" id="vpnServerStatus">Online</div>
                        <div class="stat-label">Server Status</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-users"></i></div>
                        <div class="stat-number" id="connectedClients">0</div>
                        <div class="stat-label">Connected Clients</div>
                    </div>
                </div>
                <table class="table" id="vpnTable">
                    <thead>
                        <tr>
                            <th>User ID</th>
                            <th>IP Address</th>
                            <th>Public Key</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- VPN configs will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- System Section -->
        <div class="content-section" id="system">
            <div class="section-header">
                <h2 class="section-title">System Information</h2>
                <button class="btn" onclick="checkSystemHealth()"><i class="fas fa-heartbeat"></i> Health Check</button>
            </div>
            <div class="section-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-microchip"></i></div>
                        <div class="stat-number" id="cpuUsage">0%</div>
                        <div class="stat-label">CPU Usage</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-memory"></i></div>
                        <div class="stat-number" id="memoryUsage">0%</div>
                        <div class="stat-label">Memory Usage</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-hdd"></i></div>
                        <div class="stat-number" id="diskUsage">0%</div>
                        <div class="stat-label">Disk Usage</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-clock"></i></div>
                        <div class="stat-number" id="uptime">0d</div>
                        <div class="stat-label">Uptime</div>
                    </div>
                </div>
                <div id="systemInfo"></div>
            </div>
        </div>

        <!-- Logs Section -->
        <div class="content-section" id="logs">
            <div class="section-header">
                <h2 class="section-title">System Logs</h2>
                <button class="btn" onclick="loadLogs()"><i class="fas fa-sync"></i> Refresh</button>
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label for="logLevel">Log Level:</label>
                    <select id="logLevel" class="form-control" style="width: 200px; display: inline-block;">
                        <option value="all">All</option>
                        <option value="error">Error</option>
                        <option value="warning">Warning</option>
                        <option value="info">Info</option>
                    </select>
                </div>
                <div id="logsContainer" style="background: #f8f9fa; padding: 20px; border-radius: 5px; font-family: monospace; height: 400px; overflow-y: auto;">
                    <!-- Logs will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div class="content-section" id="settings">
            <div class="section-header">
                <h2 class="section-title">System Settings</h2>
                <button class="btn" onclick="saveSettings()"><i class="fas fa-save"></i> Save Changes</button>
            </div>
            <div class="section-content">
                <form id="settingsForm">
                    <div class="form-group">
                        <label for="serverDomain">Server Domain:</label>
                        <input type="text" id="serverDomain" class="form-control" value="ciphercellvpn.com">
                    </div>
                    <div class="form-group">
                        <label for="serverIP">Server IP:</label>
                        <input type="text" id="serverIP" class="form-control" value="*************">
                    </div>
                    <div class="form-group">
                        <label for="vpnPort">VPN Port:</label>
                        <input type="number" id="vpnPort" class="form-control" value="51820">
                    </div>
                    <div class="form-group">
                        <label for="defaultPlan">Default Plan:</label>
                        <select id="defaultPlan" class="form-control">
                            <option value="basic">Basic</option>
                            <option value="premium">Premium</option>
                            <option value="unlimited">Unlimited</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Navigation
        document.querySelectorAll('.menu-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all links and sections
                document.querySelectorAll('.menu-link').forEach(l => l.classList.remove('active'));
                document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));
                
                // Add active class to clicked link
                link.classList.add('active');
                
                // Show corresponding section
                const sectionId = link.getAttribute('data-section');
                document.getElementById(sectionId).classList.add('active');
                
                // Load section data
                loadSectionData(sectionId);
            });
        });

        function loadSectionData(section) {
            switch(section) {
                case 'dashboard':
                    refreshDashboard();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'subscriptions':
                    loadSubscriptions();
                    break;
                case 'vpn':
                    loadVPNStatus();
                    break;
                case 'system':
                    checkSystemHealth();
                    break;
                case 'logs':
                    loadLogs();
                    break;
            }
        }

        // Dashboard functions
        async function refreshDashboard() {
            try {
                // Simulate API calls - replace with actual API endpoints
                document.getElementById('totalUsers').textContent = '42';
                document.getElementById('activeESIMs').textContent = '38';
                document.getElementById('vpnConnections').textContent = '25';
                document.getElementById('monthlyRevenue').textContent = '$1,250';
            } catch (error) {
                console.error('Error refreshing dashboard:', error);
            }
        }

        async function loadUsers() {
            const tbody = document.querySelector('#usersTable tbody');
            tbody.innerHTML = '<tr><td colspan="6">Loading users...</td></tr>';
            
            // Simulate loading - replace with actual API call
            setTimeout(() => {
                tbody.innerHTML = `
                    <tr>
                        <td>1</td>
                        <td>001011234567890</td>
                        <td>+31612345678</td>
                        <td><span class="status-badge status-active">Active</span></td>
                        <td>2024-01-15</td>
                        <td>
                            <button class="btn btn-sm">Edit</button>
                            <button class="btn btn-sm btn-danger">Disable</button>
                        </td>
                    </tr>
                `;
            }, 1000);
        }

        async function loadSubscriptions() {
            const tbody = document.querySelector('#subscriptionsTable tbody');
            tbody.innerHTML = '<tr><td colspan="8">Loading subscriptions...</td></tr>';
            
            setTimeout(() => {
                tbody.innerHTML = `
                    <tr>
                        <td>1</td>
                        <td>001011234567890</td>
                        <td>Premium</td>
                        <td>2024-01-15</td>
                        <td>2024-02-15</td>
                        <td>2.5GB / 5GB</td>
                        <td><span class="status-badge status-active">Active</span></td>
                        <td>
                            <button class="btn btn-sm">Extend</button>
                            <button class="btn btn-sm btn-danger">Cancel</button>
                        </td>
                    </tr>
                `;
            }, 1000);
        }

        async function loadVPNStatus() {
            document.getElementById('vpnServerStatus').textContent = 'Online';
            document.getElementById('connectedClients').textContent = '25';
            
            const tbody = document.querySelector('#vpnTable tbody');
            tbody.innerHTML = '<tr><td colspan="6">Loading VPN status...</td></tr>';
            
            setTimeout(() => {
                tbody.innerHTML = `
                    <tr>
                        <td>1</td>
                        <td>********</td>
                        <td>abc123...def456</td>
                        <td><span class="status-badge status-active">Connected</span></td>
                        <td>2024-01-15</td>
                        <td>
                            <button class="btn btn-sm btn-danger">Disconnect</button>
                        </td>
                    </tr>
                `;
            }, 1000);
        }

        async function checkSystemHealth() {
            document.getElementById('cpuUsage').textContent = '15%';
            document.getElementById('memoryUsage').textContent = '45%';
            document.getElementById('diskUsage').textContent = '32%';
            document.getElementById('uptime').textContent = '7d 12h';
            
            const systemInfo = document.getElementById('systemInfo');
            systemInfo.innerHTML = `
                <div class="alert alert-info">
                    <strong>System Health:</strong> All services are running normally.<br>
                    <strong>Last Check:</strong> ${new Date().toLocaleString()}
                </div>
            `;
        }

        async function loadLogs() {
            const container = document.getElementById('logsContainer');
            container.innerHTML = 'Loading logs...';
            
            setTimeout(() => {
                container.innerHTML = `
[2024-01-15 10:30:15] INFO: CiphercellVPN Backend started successfully<br>
[2024-01-15 10:30:16] INFO: Database connection established<br>
[2024-01-15 10:30:17] INFO: WireGuard VPN server started<br>
[2024-01-15 10:35:22] INFO: New eSIM provisioned for IMSI 001011234567890<br>
[2024-01-15 10:36:45] INFO: VPN client connected: ********<br>
[2024-01-15 10:45:12] WARNING: High memory usage detected: 85%<br>
[2024-01-15 11:00:00] INFO: Backup completed successfully<br>
                `;
            }, 1000);
        }

        function saveSettings() {
            alert('Settings saved successfully!');
        }

        // Initialize dashboard on load
        document.addEventListener('DOMContentLoaded', () => {
            refreshDashboard();
        });
    </script>
</body>
</html>