package com.ciphercell.vpn;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;

public class BootReceiver extends BroadcastReceiver {
    private static final String PREFS_NAME = "vpn_prefs";
    private static final String KEY_AUTO_CONNECT = "auto_connect";
    private static final String KEY_LAST_PROVIDER = "last_provider";
    private static final String KEY_LAST_REGION = "last_region";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            boolean autoConnect = prefs.getBoolean(KEY_AUTO_CONNECT, false);
            
            if (autoConnect) {
                String lastProvider = prefs.getString(KEY_LAST_PROVIDER, "WireGuard");
                String lastRegion = prefs.getString(KEY_LAST_REGION, "US");
                
                // Check if VPN configuration exists
                VPNConfigManager configManager = new VPNConfigManager(context);
                if (configManager.hasConfig(lastProvider)) {
                    // Start VPN service
                    Intent serviceIntent = new Intent(context, VPNService.class);
                    serviceIntent.setAction("CONNECT");
                    serviceIntent.putExtra("provider", lastProvider);
                    serviceIntent.putExtra("region", lastRegion);
                    context.startService(serviceIntent);
                }
            }
        }
    }
    
    public static void saveLastConnection(Context context, String provider, String region) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_LAST_PROVIDER, provider);
        editor.putString(KEY_LAST_REGION, region);
        editor.apply();
    }
    
    public static void setAutoConnect(Context context, boolean enabled) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putBoolean(KEY_AUTO_CONNECT, enabled).apply();
    }
    
    public static boolean isAutoConnectEnabled(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getBoolean(KEY_AUTO_CONNECT, false);
    }
}