package com.ciphercell.vpnmanager;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.MapView;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import android.widget.TextView;

public class MapFragment extends Fragment implements OnMapReadyCallback {

    private MapView mapView;
    private GoogleMap googleMap;
    private ChipGroup vpnProviderChips;
    private MaterialButton connectButton, rotateButton;
    private SwitchMaterial autoRotateSwitch;
    private TextView statusText, imsiText;
    private MaterialCardView statusCard;

    private VPNConfigManager vpnConfigManager;
    private IMSIManager imsiManager;
    private String currentProvider = "WireGuard";

    // VPN Server Locations
    private final LatLng WIREGUARD_US = new LatLng(37.7749, -122.4194);
    private final LatLng WIREGUARD_EU = new LatLng(52.5200, 13.4050);
    private final LatLng WIREGUARD_ASIA = new LatLng(35.6762, 139.6503);

    private final LatLng NORDVPN_US = new LatLng(40.7128, -74.0060);
    private final LatLng NORDVPN_EU = new LatLng(48.8566, 2.3522);
    private final LatLng NORDVPN_ASIA = new LatLng(1.3521, 103.8198);

    private final LatLng MULLVAD_US = new LatLng(34.0522, -118.2437);
    private final LatLng MULLVAD_EU = new LatLng(51.5074, -0.1278);
    private final LatLng MULLVAD_ASIA = new LatLng(22.3193, 114.1694);

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_map, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize managers
        vpnConfigManager = new VPNConfigManager(requireContext());
        imsiManager = new IMSIManager(requireContext());

        // Initialize views
        mapView = view.findViewById(R.id.map_view);
        vpnProviderChips = view.findViewById(R.id.vpn_provider_chips);
        connectButton = view.findViewById(R.id.connect_button);
        rotateButton = view.findViewById(R.id.rotate_button);
        autoRotateSwitch = view.findViewById(R.id.auto_rotate_switch);
        statusText = view.findViewById(R.id.status_text);
        imsiText = view.findViewById(R.id.imsi_text);
        statusCard = view.findViewById(R.id.status_card);

        mapView.onCreate(savedInstanceState);
        mapView.getMapAsync(this);

        setupListeners();
        updateUI();
    }

    private void setupListeners() {
        vpnProviderChips.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.chip_wireguard) {
                currentProvider = "WireGuard";
            } else if (checkedId == R.id.chip_nordvpn) {
                currentProvider = "NordVPN";
            } else if (checkedId == R.id.chip_mullvad) {
                currentProvider = "Mullvad";
            }
            updateMapMarkers();
        });

        connectButton.setOnClickListener(v -> {
            if (connectButton.getText().equals(getString(R.string.connect))) {
                connectVPN();
            } else {
                disconnectVPN();
            }
        });

        rotateButton.setOnClickListener(v -> rotateConnection());

        autoRotateSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Handle auto-rotation setting
            vpnConfigManager.setAutoRotate(isChecked);
        });
    }

    private void connectVPN() {
        // Implement VPN connection logic
        statusText.setText(R.string.status_connecting);
        // Add actual VPN connection implementation
    }

    private void disconnectVPN() {
        // Implement VPN disconnection logic
        statusText.setText(R.string.status_disconnecting);
        // Add actual VPN disconnection implementation
    }

    private void rotateConnection() {
        // Implement connection rotation logic
        statusText.setText(R.string.status_rotating);
        // Add actual rotation implementation
    }

    private void updateUI() {
        boolean isConnected = vpnConfigManager.isConnected();
        connectButton.setText(isConnected ? R.string.disconnect : R.string.connect);
        statusText.setText(isConnected ? R.string.status_connected : R.string.status_disconnected);
        imsiText.setText(getString(R.string.imsi_format, imsiManager.getCurrentIMSI()));
    }

    private void updateMapMarkers() {
        if (googleMap == null) return;

        googleMap.clear();

        LatLng usLocation, euLocation, asiaLocation;
        switch (currentProvider) {
            case "NordVPN":
                usLocation = NORDVPN_US;
                euLocation = NORDVPN_EU;
                asiaLocation = NORDVPN_ASIA;
                break;
            case "Mullvad":
                usLocation = MULLVAD_US;
                euLocation = MULLVAD_EU;
                asiaLocation = MULLVAD_ASIA;
                break;
            default: // WireGuard
                usLocation = WIREGUARD_US;
                euLocation = WIREGUARD_EU;
                asiaLocation = WIREGUARD_ASIA;
                break;
        }

        googleMap.addMarker(new MarkerOptions().position(usLocation).title("US Server"));
        googleMap.addMarker(new MarkerOptions().position(euLocation).title("EU Server"));
        googleMap.addMarker(new MarkerOptions().position(asiaLocation).title("Asia Server"));
    }

    @Override
    public void onMapReady(GoogleMap map) {
        googleMap = map;
        updateMapMarkers();
    }

    @Override
    public void onResume() {
        super.onResume();
        mapView.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        mapView.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mapView.onDestroy();
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        mapView.onLowMemory();
    }
}