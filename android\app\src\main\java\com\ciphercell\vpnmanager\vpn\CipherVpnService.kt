package com.ciphercell.vpnmanager.vpn

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.VpnService
import android.os.Build
import androidx.core.app.NotificationCompat
import com.ciphercell.vpnmanager.MainActivity
import com.ciphercell.vpnmanager.R
import com.wireguard.config.Config
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class CipherVpnService : VpnService() {

    @Inject
    lateinit var vpnManager: VpnManager

    private val notificationManager by lazy {
        getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_CONNECT -> {
                val configString = intent.getStringExtra(EXTRA_CONFIG)
                if (configString != null) {
                    val config = Config.parse(configString.reader())
                    vpnManager.connect(config)
                    startForeground(NOTIFICATION_ID, createNotification("Connected"))
                }
            }
            ACTION_DISCONNECT -> {
                vpnManager.disconnect()
                stopForeground(true)
                stopSelf()
            }
        }
        return START_STICKY
    }

    private fun createNotification(text: String): Notification {
        val pendingIntent: PendingIntent = Intent(this, MainActivity::class.java).let {
            PendingIntent.getActivity(this, 0, it, PendingIntent.FLAG_IMMUTABLE)
        }

        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("CipherCell VPN")
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_lock)
            .setContentIntent(pendingIntent)
            .build()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = getString(R.string.notification_channel_name)
            val descriptionText = getString(R.string.notification_channel_description)
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            notificationManager.createNotificationChannel(channel)
        }
    }

    override fun onDestroy() {
        vpnManager.disconnect()
        super.onDestroy()
    }

    companion object {
        const val ACTION_CONNECT = "com.ciphercell.vpnmanager.CONNECT"
        const val ACTION_DISCONNECT = "com.ciphercell.vpnmanager.DISCONNECT"
        const val EXTRA_CONFIG = "com.ciphercell.vpnmanager.CONFIG"

        private const val NOTIFICATION_ID = 1
        private const val NOTIFICATION_CHANNEL_ID = "CipherVpnServiceChannel"
    }
}
