// CipherCell VPN - Enhanced JavaScript Functionality

class Cipher<PERSON>ellApp {
    constructor() {
        this.apiBase = '/api';
        this.wsConnection = null;
        this.notifications = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.startPeriodicUpdates();
        this.setupWebSocket();
    }

    setupEventListeners() {
        // Form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Button clicks
        document.addEventListener('click', this.handleButtonClick.bind(this));
        
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', this.switchTab.bind(this));
        });
        
        // Mobile menu toggle
        const mobileToggle = document.querySelector('.mobile-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', this.toggleMobileMenu.bind(this));
        }
        
        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Page visibility change
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }

    initializeComponents() {
        this.loadDashboardData();
        this.initializeCharts();
        this.setupFormValidation();
        this.loadUserPreferences();
    }

    // API Methods
    async apiCall(endpoint, method = 'GET', data = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, options);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            this.showNotification('API call failed: ' + error.message, 'error');
            throw error;
        }
    }

    // Dashboard Data Loading
    async loadDashboardData() {
        try {
            this.showLoading('dashboard-stats');
            
            const [stats, recentActivity] = await Promise.all([
                this.apiCall('/stats'),
                this.apiCall('/activity/recent')
            ]);
            
            this.updateDashboardStats(stats);
            this.updateRecentActivity(recentActivity);
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        } finally {
            this.hideLoading('dashboard-stats');
        }
    }

    updateDashboardStats(stats) {
        const elements = {
            'total-users': stats.totalUsers || 0,
            'active-esims': stats.activeESIMs || 0,
            'vpn-connections': stats.vpnConnections || 0,
            'data-usage': this.formatBytes(stats.dataUsage || 0)
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateNumber(element, value);
            }
        });
    }

    updateRecentActivity(activities) {
        const container = document.getElementById('recent-activity');
        if (!container) return;

        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${this.formatTime(activity.timestamp)}</div>
                </div>
            </div>
        `).join('');
    }

    // eSIM Provisioning
    async provisionESIM(formData) {
        try {
            this.showLoading('provision-form');
            
            const result = await this.apiCall('/esim/provision', 'POST', formData);
            
            if (result.success) {
                this.showNotification('eSIM provisioned successfully!', 'success');
                this.displayQRCode(result.qrCode);
                this.updateProvisioningStatus(result);
            } else {
                throw new Error(result.message || 'Provisioning failed');
            }
            
        } catch (error) {
            this.showNotification('Provisioning failed: ' + error.message, 'error');
        } finally {
            this.hideLoading('provision-form');
        }
    }

    displayQRCode(qrCodeData) {
        const modal = document.getElementById('qr-modal');
        const qrContainer = document.getElementById('qr-code-container');
        
        if (modal && qrContainer) {
            qrContainer.innerHTML = `<img src="data:image/png;base64,${qrCodeData}" alt="eSIM QR Code" class="qr-code-image">`;
            this.showModal('qr-modal');
        }
    }

    // VPN Management
    async generateVPNConfig(userId) {
        try {
            this.showLoading('vpn-section');
            
            const config = await this.apiCall('/vpn/generate', 'POST', { userId });
            
            if (config.success) {
                this.displayVPNConfig(config.data);
                this.showNotification('VPN configuration generated!', 'success');
            }
            
        } catch (error) {
            this.showNotification('Failed to generate VPN config: ' + error.message, 'error');
        } finally {
            this.hideLoading('vpn-section');
        }
    }

    displayVPNConfig(configData) {
        const container = document.getElementById('vpn-config-display');
        if (!container) return;

        container.innerHTML = `
            <div class="config-section">
                <h4>WireGuard Configuration</h4>
                <div class="config-content">
                    <pre><code>${configData.config}</code></pre>
                </div>
                <div class="config-actions">
                    <button class="btn btn-enhanced" onclick="app.downloadConfig('${configData.filename}', '${configData.config}')">
                        <i class="fas fa-download"></i> Download Config
                    </button>
                    <button class="btn btn-enhanced" onclick="app.showQRCode('${configData.qrCode}')">
                        <i class="fas fa-qrcode"></i> Show QR Code
                    </button>
                </div>
            </div>
        `;
    }

    downloadConfig(filename, content) {
        const blob = new Blob([content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // Subscription Management
    async loadSubscriptions() {
        try {
            const subscriptions = await this.apiCall('/subscriptions');
            this.displaySubscriptions(subscriptions);
        } catch (error) {
            console.error('Failed to load subscriptions:', error);
        }
    }

    displaySubscriptions(subscriptions) {
        const container = document.getElementById('subscriptions-list');
        if (!container) return;

        container.innerHTML = subscriptions.map(sub => `
            <div class="subscription-card">
                <div class="subscription-header">
                    <h4>${sub.plan}</h4>
                    <span class="status-indicator status-${sub.status}">${sub.status}</span>
                </div>
                <div class="subscription-details">
                    <p><strong>IMSI:</strong> ${sub.imsi}</p>
                    <p><strong>Expires:</strong> ${this.formatDate(sub.expiryDate)}</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${sub.dataUsagePercent}%"></div>
                    </div>
                    <p class="usage-text">${this.formatBytes(sub.dataUsed)} / ${this.formatBytes(sub.dataLimit)}</p>
                </div>
                <div class="subscription-actions">
                    <button class="btn btn-sm" onclick="app.extendSubscription('${sub.id}')">
                        <i class="fas fa-clock"></i> Extend
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.cancelSubscription('${sub.id}')">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Form Handling
    handleFormSubmit(event) {
        const form = event.target;
        if (!form.matches('form[data-api]')) return;
        
        event.preventDefault();
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        const endpoint = form.dataset.api;
        
        switch (endpoint) {
            case 'provision':
                this.provisionESIM(data);
                break;
            case 'vpn-generate':
                this.generateVPNConfig(data.userId);
                break;
            case 'subscription':
                this.createSubscription(data);
                break;
            default:
                console.warn('Unknown form endpoint:', endpoint);
        }
    }

    handleButtonClick(event) {
        const button = event.target.closest('button[data-action]');
        if (!button) return;
        
        const action = button.dataset.action;
        const target = button.dataset.target;
        
        switch (action) {
            case 'refresh':
                this.refreshSection(target);
                break;
            case 'modal':
                this.showModal(target);
                break;
            case 'close-modal':
                this.hideModal(target);
                break;
            case 'copy':
                this.copyToClipboard(button.dataset.content);
                break;
        }
    }

    // UI Utilities
    showLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.add('loading');
        }
    }

    hideLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.remove('loading');
        }
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; margin-left: 10px; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('Copied to clipboard!', 'success', 2000);
        }).catch(() => {
            this.showNotification('Failed to copy to clipboard', 'error');
        });
    }

    // Animation Utilities
    animateNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * this.easeOutCubic(progress));
            element.textContent = currentValue;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    // Utility Functions
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    formatTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;
        
        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
        return this.formatDate(timestamp);
    }

    getActivityIcon(type) {
        const icons = {
            'esim_provision': 'sim-card',
            'vpn_connect': 'shield-alt',
            'subscription': 'credit-card',
            'payment': 'dollar-sign',
            'error': 'exclamation-triangle'
        };
        return icons[type] || 'info-circle';
    }

    // WebSocket for real-time updates
    setupWebSocket() {
        if (!window.WebSocket) return;
        
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        try {
            this.wsConnection = new WebSocket(wsUrl);
            
            this.wsConnection.onopen = () => {
                console.log('WebSocket connected');
            };
            
            this.wsConnection.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.wsConnection.onclose = () => {
                console.log('WebSocket disconnected');
                // Attempt to reconnect after 5 seconds
                setTimeout(() => this.setupWebSocket(), 5000);
            };
            
        } catch (error) {
            console.warn('WebSocket connection failed:', error);
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'stats_update':
                this.updateDashboardStats(data.stats);
                break;
            case 'new_activity':
                this.addActivityItem(data.activity);
                break;
            case 'notification':
                this.showNotification(data.message, data.level);
                break;
        }
    }

    // Periodic Updates
    startPeriodicUpdates() {
        // Update dashboard every 30 seconds
        setInterval(() => {
            if (!document.hidden) {
                this.loadDashboardData();
            }
        }, 30000);
    }

    handleVisibilityChange() {
        if (!document.hidden) {
            // Page became visible, refresh data
            this.loadDashboardData();
        }
    }

    handleResize() {
        // Handle responsive layout changes
        this.updateChartSizes();
    }

    // Chart Management
    initializeCharts() {
        // Initialize charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            this.createUsageChart();
            this.createRevenueChart();
        }
    }

    createUsageChart() {
        const ctx = document.getElementById('usageChart');
        if (!ctx) return;
        
        // Chart implementation would go here
        // This is a placeholder for actual chart library integration
    }

    updateChartSizes() {
        // Update chart sizes on window resize
        if (this.charts) {
            Object.values(this.charts).forEach(chart => {
                if (chart.resize) chart.resize();
            });
        }
    }

    // Form Validation
    setupFormValidation() {
        document.querySelectorAll('form[data-validate]').forEach(form => {
            form.addEventListener('input', this.validateForm.bind(this));
        });
    }

    validateForm(event) {
        const form = event.target.closest('form');
        if (!form) return;
        
        const field = event.target;
        this.validateField(field);
    }

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        
        let isValid = true;
        let message = '';
        
        if (required && !value) {
            isValid = false;
            message = 'This field is required';
        } else if (value) {
            switch (type) {
                case 'email':
                    isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                    message = 'Please enter a valid email address';
                    break;
                case 'tel':
                    isValid = /^\+?[1-9]\d{1,14}$/.test(value.replace(/\s/g, ''));
                    message = 'Please enter a valid phone number';
                    break;
            }
        }
        
        this.setFieldValidation(field, isValid, message);
        return isValid;
    }

    setFieldValidation(field, isValid, message) {
        const container = field.closest('.form-floating') || field.parentElement;
        const errorElement = container.querySelector('.field-error');
        
        if (isValid) {
            field.classList.remove('invalid');
            field.classList.add('valid');
            if (errorElement) errorElement.remove();
        } else {
            field.classList.remove('valid');
            field.classList.add('invalid');
            
            if (!errorElement && message) {
                const error = document.createElement('div');
                error.className = 'field-error';
                error.textContent = message;
                container.appendChild(error);
            }
        }
    }

    // User Preferences
    loadUserPreferences() {
        const preferences = localStorage.getItem('ciphercell_preferences');
        if (preferences) {
            try {
                const prefs = JSON.parse(preferences);
                this.applyPreferences(prefs);
            } catch (error) {
                console.warn('Failed to load user preferences:', error);
            }
        }
    }

    saveUserPreferences(preferences) {
        localStorage.setItem('ciphercell_preferences', JSON.stringify(preferences));
    }

    applyPreferences(preferences) {
        if (preferences.theme) {
            document.body.setAttribute('data-theme', preferences.theme);
        }
        if (preferences.notifications !== undefined) {
            this.notificationsEnabled = preferences.notifications;
        }
    }
}

// Initialize the application
const app = new CipherCellApp();

// Global utility functions
window.app = app;

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}