{% extends "admin/base.html" %}

{% block title %}User Management{% endblock %}
{% block header %}User Management{% endblock %}

{% block content %}
<!-- Action Buttons -->
<div class="mb-6 flex justify-between items-center">
    <button onclick="showAddUserModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
        <i class="fas fa-user-plus mr-2"></i> Add New User
    </button>
    <div class="flex items-center space-x-4">
        <div class="relative">
            <input type="text" id="search" placeholder="Search users..." 
                   class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
        </div>
        <select id="roleFilter" class="border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="all">All Roles</option>
            <option value="admin">Admin</option>
            <option value="user">User</option>
        </select>
    </div>
</div>

<!-- Users List -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for user in users %}
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ user.id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.username }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if user.role == 'admin' %}bg-purple-100 text-purple-800
                        {% else %}bg-blue-100 text-blue-800{% endif %}">
                        {{ user.role }}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.last_login|default('Never') }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if user.status == 'active' %}bg-green-100 text-green-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        {{ user.status|default('active') }}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button onclick="showEditModal('{{ user.id }}')" class="text-blue-600 hover:text-blue-900">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="resetPassword('{{ user.id }}')" class="text-yellow-600 hover:text-yellow-900">
                        <i class="fas fa-key"></i>
                    </button>
                    {% if user.username != 'admin' %}
                    <button onclick="toggleUserStatus('{{ user.id }}')" class="{% if user.status == 'active' %}text-red-600 hover:text-red-900{% else %}text-green-600 hover:text-green-900{% endif %}">
                        <i class="fas {% if user.status == 'active' %}fa-user-slash{% else %}fa-user-check{% endif %}"></i>
                    </button>
                    <button onclick="confirmDelete('{{ user.id }}')" class="text-red-600 hover:text-red-900">
                        <i class="fas fa-trash"></i>
                    </button>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Add/Edit User Modal -->
<div id="userModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modalTitle">Add New User</h3>
            <form id="userForm" class="space-y-4">
                <input type="hidden" id="userId" name="id">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Username</label>
                    <input type="text" id="username" name="username" required 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" id="password" name="password" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Role</label>
                    <select id="role" name="role" required 
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-4 mt-6">
                    <button type="button" onclick="hideUserModal()" 
                            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">Cancel</button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showAddUserModal() {
    document.getElementById('modalTitle').textContent = 'Add New User';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('userModal').classList.remove('hidden');
}

function showEditModal(userId) {
    document.getElementById('modalTitle').textContent = 'Edit User';
    // Fetch and populate user data
    document.getElementById('userId').value = userId;
    document.getElementById('userModal').classList.remove('hidden');
}

function hideUserModal() {
    document.getElementById('userModal').classList.add('hidden');
}

function resetPassword(userId) {
    if (confirm('Are you sure you want to reset this user\'s password?')) {
        // Implement password reset logic
    }
}

function toggleUserStatus(userId) {
    if (confirm('Are you sure you want to change this user\'s status?')) {
        // Implement status toggle logic
    }
}

function confirmDelete(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        // Implement delete logic
    }
}

document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Implement form submission logic
});

document.getElementById('search').addEventListener('input', function(e) {
    // Implement search logic
});

document.getElementById('roleFilter').addEventListener('change', function(e) {
    // Implement role filter logic
});
</script>
{% endblock %}