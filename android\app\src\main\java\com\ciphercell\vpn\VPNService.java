package com.ciphercell.vpn;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.net.VpnService;
import android.os.Build;
import android.os.ParcelFileDescriptor;

import androidx.core.app.NotificationCompat;

public class VPNService extends VpnService {
    private static final String CHANNEL_ID = "vpn_service_channel";
    private static final int NOTIFICATION_ID = 1;
    
    private ParcelFileDescriptor vpnInterface = null;
    private String currentProvider = null;
    private String currentRegion = null;
    private boolean isConnected = false;
    
    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            if (action != null) {
                switch (action) {
                    case "CONNECT":
                        currentProvider = intent.getStringExtra("provider");
                        currentRegion = intent.getStringExtra("region");
                        connect();
                        break;
                    case "DISCONNECT":
                        disconnect();
                        break;
                }
            }
        }
        return START_STICKY;
    }
    
    private void connect() {
        try {
            // Build VPN interface
            VpnService.Builder builder = new VpnService.Builder()
                .setSession(getString(R.string.app_name))
                .addAddress("********", 32)
                .addDnsServer("*******")
                .addRoute("0.0.0.0", 0);
            
            // Allow apps to bypass VPN
            builder.allowBypass();
            
            // Close previous interface if exists
            if (vpnInterface != null) {
                try {
                    vpnInterface.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            
            // Establish new interface
            vpnInterface = builder.establish();
            if (vpnInterface != null) {
                isConnected = true;
                updateNotification(true);
                // Start connection specific to VPN provider
                switch (currentProvider) {
                    case "WireGuard":
                        startWireGuardConnection();
                        break;
                    case "NordVPN":
                        startNordVPNConnection();
                        break;
                    case "Mullvad":
                        startMullvadConnection();
                        break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            disconnect();
        }
    }
    
    private void disconnect() {
        if (vpnInterface != null) {
            try {
                vpnInterface.close();
                vpnInterface = null;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        isConnected = false;
        updateNotification(false);
        stopForeground(true);
        stopSelf();
    }
    
    private void startWireGuardConnection() {
        // Implement WireGuard specific connection logic
    }
    
    private void startNordVPNConnection() {
        // Implement NordVPN specific connection logic
    }
    
    private void startMullvadConnection() {
        // Implement Mullvad specific connection logic
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("VPN connection status");
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    private void updateNotification(boolean connected) {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE);
        
        String status = connected ? 
            String.format(getString(R.string.msg_connected), currentProvider) :
            getString(R.string.msg_disconnected);
        
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(status)
            .setSmallIcon(R.drawable.ic_vpn)
            .setContentIntent(pendingIntent)
            .build();
        
        if (connected) {
            startForeground(NOTIFICATION_ID, notification);
        } else {
            stopForeground(true);
        }
    }
    
    @Override
    public void onDestroy() {
        disconnect();
        super.onDestroy();
    }
    
    @Override
    public void onRevoke() {
        disconnect();
        super.onRevoke();
    }
}