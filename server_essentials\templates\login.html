{% extends "base.html" %}

{% block title %}Inloggen - SM-DP+ Admin{% endblock %}

{% block login_content %}
<div class="card shadow">
    <div class="card-body p-5">
        <div class="text-center mb-4">
            <i class="fas fa-sim-card fa-3x text-primary mb-3"></i>
            <h3 class="card-title">SM-DP+ Admin Dashboard</h3>
            <p class="text-muted">Log in om toegang te krijgen tot het beheerpaneel</p>
        </div>
        
        <form method="POST">
            <div class="mb-3">
                <label for="username" class="form-label">
                    <i class="fas fa-user"></i> Gebruikersnaam
                </label>
                <input type="text" class="form-control form-control-lg" id="username" name="username" required autocomplete="username">
            </div>
            
            <div class="mb-4">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i> Wachtwoord
                </label>
                <input type="password" class="form-control form-control-lg" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt"></i> Inloggen
                </button>
            </div>
        </form>
        
        <div class="text-center mt-4">
            <small class="text-muted">
                <i class="fas fa-shield-alt"></i> Beveiligde verbinding
            </small>
        </div>
    </div>
</div>

<div class="text-center mt-4">
    <small class="text-muted">
        SM-DP+ Server Management Dashboard<br>
        Voor technische ondersteuning, neem contact op met de systeembeheerder.
    </small>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-focus op username veld
document.getElementById('username').focus();

// Enter key support
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.querySelector('form').submit();
    }
});
</script>
{% endblock %}