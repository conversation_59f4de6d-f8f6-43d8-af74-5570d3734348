pluginManagement {
    plugins {
        id 'com.android.application' version '8.1.1' apply false
        id 'org.jetbrains.kotlin.android' version '1.8.0' apply false
        id 'org.jetbrains.kotlin.kapt' version '1.8.0' apply false
        id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin' version '2.0.1' apply false
        id 'com.google.dagger.hilt.android' version '2.48.1' apply false
    }

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.name = "CipherCellVPN"
include ':app'

