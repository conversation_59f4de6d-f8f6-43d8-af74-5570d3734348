{% extends "base.html" %}

{% block title %}Nieuw eSIM Profiel - SM-DP+ Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus-circle"></i> Nieuw eSIM Profiel Genereren
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('profiles') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Terug naar Profielen
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Profiel Generatie Form -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cog"></i> Profiel Configuratie
                </h6>
            </div>
            <div class="card-body">
                <form id="generateProfileForm" method="POST">
                    <!-- Basis Configuratie -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="activationCode" class="form-label">Activatiecode</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="activationCode" name="activation_code" 
                                       value="{{ activation_code or '' }}" placeholder="Automatisch gegenereerd">
                                <button class="btn btn-outline-secondary" type="button" onclick="generateActivationCode()">
                                    <i class="fas fa-dice"></i>
                                </button>
                            </div>
                            <div class="form-text">Laat leeg voor automatische generatie</div>
                        </div>
                        <div class="col-md-6">
                            <label for="profileName" class="form-label">Profiel Naam</label>
                            <input type="text" class="form-control" id="profileName" name="profile_name" 
                                   placeholder="Bijv. KPN Business eSIM">
                        </div>
                    </div>

                    <!-- IMSI/IMEI Configuratie -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="imsi" class="form-label">IMSI</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="imsi" name="imsi" 
                                       placeholder="Automatisch gegenereerd">
                                <button class="btn btn-outline-secondary" type="button" onclick="generateIMSI()">
                                    <i class="fas fa-dice"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="imei" class="form-label">IMEI</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="imei" name="imei" 
                                       placeholder="Automatisch gegenereerd">
                                <button class="btn btn-outline-secondary" type="button" onclick="generateIMEI()">
                                    <i class="fas fa-dice"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- MCC/MNC Configuratie -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="mcc" class="form-label">MCC (Mobile Country Code)</label>
                            <select class="form-select" id="mcc" name="mcc">
                                <option value="204">204 - Nederland</option>
                                <option value="206">206 - België</option>
                                <option value="208">208 - Frankrijk</option>
                                <option value="262">262 - Duitsland</option>
                                <option value="234">234 - Verenigd Koninkrijk</option>
                                <option value="310">310 - Verenigde Staten</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="mnc" class="form-label">MNC (Mobile Network Code)</label>
                            <select class="form-select" id="mnc" name="mnc">
                                <option value="08">08 - KPN</option>
                                <option value="01">01 - Vodafone</option>
                                <option value="20">20 - T-Mobile</option>
                                <option value="99">99 - Custom MVNO</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="tac" class="form-label">TAC (Type Allocation Code)</label>
                            <input type="text" class="form-control" id="tac" name="tac" 
                                   value="35693800" placeholder="35693800">
                        </div>
                    </div>

                    <!-- VPN Configuratie -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-shield-alt"></i> VPN Configuratie (Optioneel)
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="enableVPN" name="enable_vpn" onchange="toggleVPNConfig()">
                                <label class="form-check-label" for="enableVPN">
                                    VPN configuratie toevoegen aan eSIM profiel
                                </label>
                            </div>
                            
                            <div id="vpnConfig" style="display: none;">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="vpnType" class="form-label">VPN Type</label>
                                        <select class="form-select" id="vpnType" name="vpn_type">
                                            <option value="wireguard">WireGuard</option>
                                            <option value="openvpn">OpenVPN</option>
                                            <option value="ikev2">IKEv2</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="vpnServer" class="form-label">VPN Server</label>
                                        <input type="text" class="form-control" id="vpnServer" name="vpn_server" 
                                               placeholder="vpn.ciphercellvpn.com">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="vpnPort" class="form-label">Poort</label>
                                        <input type="number" class="form-control" id="vpnPort" name="vpn_port" 
                                               value="51820" min="1" max="65535">
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="vpnUsername" class="form-label">Gebruikersnaam</label>
                                        <input type="text" class="form-control" id="vpnUsername" name="vpn_username">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="vpnPassword" class="form-label">Wachtwoord</label>
                                        <input type="password" class="form-control" id="vpnPassword" name="vpn_password">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Geavanceerde Opties -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cogs"></i> Geavanceerde Opties
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="expiryDays" class="form-label">Vervaldatum (dagen)</label>
                                    <input type="number" class="form-control" id="expiryDays" name="expiry_days" 
                                           value="30" min="1" max="365">
                                    <div class="form-text">Aantal dagen voordat het profiel verloopt</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="maxActivations" class="form-label">Max Activaties</label>
                                    <input type="number" class="form-control" id="maxActivations" name="max_activations" 
                                           value="1" min="1" max="10">
                                    <div class="form-text">Aantal keer dat het profiel geactiveerd kan worden</div>
                                </div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="encryptProfile" name="encrypt_profile" checked>
                                <label class="form-check-label" for="encryptProfile">
                                    Profiel versleutelen met Fernet
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="generateQR" name="generate_qr" checked>
                                <label class="form-check-label" for="generateQR">
                                    QR-code automatisch genereren
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle"></i> Profiel Genereren
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Preview Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-eye"></i> Live Preview
                </h6>
            </div>
            <div class="card-body">
                <div id="profilePreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                        <p>Vul de velden in om een preview te zien</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-question-circle"></i> Help & Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="accordion" id="helpAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingIMSI">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseIMSI">
                                IMSI Nummers
                            </button>
                        </h2>
                        <div id="collapseIMSI" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <small>
                                    IMSI = MCC + MNC + MSIN<br>
                                    • MCC: Land code (3 cijfers)<br>
                                    • MNC: Netwerk code (2-3 cijfers)<br>
                                    • MSIN: Abonnee ID (9-10 cijfers)
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingIMEI">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseIMEI">
                                IMEI Nummers
                            </button>
                        </h2>
                        <div id="collapseIMEI" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <small>
                                    IMEI = TAC + SNR + CD<br>
                                    • TAC: Type code (8 cijfers)<br>
                                    • SNR: Serie nummer (6 cijfers)<br>
                                    • CD: Check digit (1 cijfer)
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingVPN">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseVPN">
                                VPN Configuratie
                            </button>
                        </h2>
                        <div id="collapseVPN" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <small>
                                    • WireGuard: Moderne, snelle VPN<br>
                                    • OpenVPN: Compatibel, betrouwbaar<br>
                                    • IKEv2: Ingebouwd in iOS/Android
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Form validation en preview functionaliteit
document.getElementById('generateProfileForm').addEventListener('submit', function(e) {
    // Basic validation
    const activationCode = document.getElementById('activationCode').value;
    if (!activationCode) {
        e.preventDefault();
        showToast('Activatiecode is verplicht', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Genereren...';
    submitBtn.disabled = true;
    
    // Allow form to submit normally to backend
    // The backend will handle the actual profile generation
});

// Generate random activation code
function generateActivationCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 12; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('activationCode').value = result;
    updatePreview();
}

// Generate IMSI
function generateIMSI() {
    const mcc = document.getElementById('mcc').value;
    const mnc = document.getElementById('mnc').value;
    const msin = Math.floor(Math.random() * 10000000000).toString().padStart(10, '0');
    const imsi = mcc + mnc + msin;
    document.getElementById('imsi').value = imsi;
    updatePreview();
}

// Generate IMEI
function generateIMEI() {
    const tac = document.getElementById('tac').value || '35693800';
    const snr = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    const partial = tac + snr;
    
    // Calculate Luhn checksum
    let sum = 0;
    for (let i = 0; i < partial.length; i++) {
        let digit = parseInt(partial[i]);
        if (i % 2 === 1) {
            digit *= 2;
            if (digit > 9) digit = Math.floor(digit / 10) + (digit % 10);
        }
        sum += digit;
    }
    const checkDigit = (10 - (sum % 10)) % 10;
    
    const imei = partial + checkDigit;
    document.getElementById('imei').value = imei;
    updatePreview();
}

// Toggle VPN configuration
function toggleVPNConfig() {
    const checkbox = document.getElementById('enableVPN');
    const config = document.getElementById('vpnConfig');
    config.style.display = checkbox.checked ? 'block' : 'none';
    updatePreview();
}

// Update preview
function updatePreview() {
    const activationCode = document.getElementById('activationCode').value;
    const profileName = document.getElementById('profileName').value;
    const imsi = document.getElementById('imsi').value;
    const imei = document.getElementById('imei').value;
    const enableVPN = document.getElementById('enableVPN').checked;
    
    let preview = `
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">${profileName || 'Nieuw eSIM Profiel'}</h6>
                <hr>
    `;
    
    if (activationCode) {
        preview += `<p><strong>Code:</strong><br><code>${activationCode}</code></p>`;
        preview += `<p><strong>LPA URI:</strong><br><small>LPA:1$sm-dp+.ciphercellvpn.com$${activationCode}</small></p>`;
    }
    
    if (imsi) {
        preview += `<p><strong>IMSI:</strong><br><small>${imsi}</small></p>`;
    }
    
    if (imei) {
        preview += `<p><strong>IMEI:</strong><br><small>${imei}</small></p>`;
    }
    
    if (enableVPN) {
        const vpnType = document.getElementById('vpnType').value;
        preview += `<p><strong>VPN:</strong><br><span class="badge bg-success">${vpnType.toUpperCase()}</span></p>`;
    }
    
    preview += `
            </div>
        </div>
    `;
    
    document.getElementById('profilePreview').innerHTML = preview;
}

// Reset form
function resetForm() {
    document.getElementById('generateProfileForm').reset();
    document.getElementById('vpnConfig').style.display = 'none';
    updatePreview();
}

// Toast notification function
function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Initialize form
document.addEventListener('DOMContentLoaded', function() {
    // Generate initial values
    generateActivationCode();
    generateIMSI();
    generateIMEI();
    
    // Add event listeners for live preview
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('input', updatePreview);
        input.addEventListener('change', updatePreview);
    });
    
    updatePreview();
});
</script>
{% endblock %}