{% extends "base.html" %}

{% block title %}Audit Log - SM-DP+ Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-clipboard-list"></i> Audit Log
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i> Vernieuwen
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="exportLogs()">
                <i class="fas fa-download"></i> Exporteren
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="clearOldLogs()">
                <i class="fas fa-trash"></i> Oude Logs Wissen
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="actionFilter" class="form-label">Actie Filter</label>
                <select class="form-select" id="actionFilter" onchange="filterLogs()">
                    <option value="">Alle Acties</option>
                    <option value="login">Login</option>
                    <option value="logout">Logout</option>
                    <option value="profile_created">Profiel Aangemaakt</option>
                    <option value="profile_activated">Profiel Geactiveerd</option>
                    <option value="qr_generated">QR-code Gegenereerd</option>
                    <option value="profile_deleted">Profiel Verwijderd</option>
                    <option value="config_changed">Configuratie Gewijzigd</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="userFilter" class="form-label">Gebruiker</label>
                <input type="text" class="form-control" id="userFilter" placeholder="admin, system..." onkeyup="filterLogs()">
            </div>
            <div class="col-md-2">
                <label for="dateFromFilter" class="form-label">Van Datum</label>
                <input type="date" class="form-control" id="dateFromFilter" onchange="filterLogs()">
            </div>
            <div class="col-md-2">
                <label for="dateToFilter" class="form-label">Tot Datum</label>
                <input type="date" class="form-control" id="dateToFilter" onchange="filterLogs()">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Wissen
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Totaal Logs
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalLogs">
                            {{ logs|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Vandaag
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayLogs">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Logins
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="loginCount">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-sign-in-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Profielen
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="profileCount">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-id-card fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audit Log Tabel -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-table"></i> Audit Log Entries
            <span class="badge bg-secondary ms-2" id="logCount">{{ logs|length }}</span>
        </h6>
    </div>
    <div class="card-body">
        {% if logs %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="auditTable">
                <thead class="table-light">
                    <tr>
                        <th>Timestamp</th>
                        <th>Gebruiker</th>
                        <th>Actie</th>
                        <th>Details</th>
                        <th>IP Adres</th>
                        <th>User Agent</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs %}
                    <tr data-action="{{ log[2] }}" data-user="{{ log[1] }}" data-date="{{ log[0][:10] }}">
                        <td>
                            <small class="text-muted">{{ log[0]|datetime }}</small>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ log[1] }}</span>
                        </td>
                        <td>
                            {% if log[2] == 'login' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </span>
                            {% elif log[2] == 'logout' %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </span>
                            {% elif log[2] == 'profile_created' %}
                                <span class="badge bg-primary">
                                    <i class="fas fa-plus-circle"></i> Profiel Aangemaakt
                                </span>
                            {% elif log[2] == 'profile_activated' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-check-circle"></i> Profiel Geactiveerd
                                </span>
                            {% elif log[2] == 'qr_generated' %}
                                <span class="badge bg-info">
                                    <i class="fas fa-qrcode"></i> QR Gegenereerd
                                </span>
                            {% elif log[2] == 'profile_deleted' %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-trash"></i> Profiel Verwijderd
                                </span>
                            {% else %}
                                <span class="badge bg-light text-dark">
                                    <i class="fas fa-cog"></i> {{ log[2] }}
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ log[3] }}</small>
                        </td>
                        <td>
                            <code class="text-muted">{{ log[4] or 'N/A' }}</code>
                        </td>
                        <td>
                            <small class="text-muted" title="{{ log[5] or 'N/A' }}">
                                {% if log[5] %}
                                    {{ log[5][:50] }}{% if log[5]|length > 50 %}...{% endif %}
                                {% else %}
                                    N/A
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            {% if log[6] == 'success' %}
                                <span class="badge bg-success">Success</span>
                            {% elif log[6] == 'failed' %}
                                <span class="badge bg-danger">Failed</span>
                            {% elif log[6] == 'warning' %}
                                <span class="badge bg-warning">Warning</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ log[6] or 'Unknown' }}</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Paginatie -->
        <nav aria-label="Audit log paginatie" class="mt-3">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <span class="page-link">Vorige</span>
                </li>
                <li class="page-item active">
                    <span class="page-link">1</span>
                </li>
                <li class="page-item disabled">
                    <span class="page-link">Volgende</span>
                </li>
            </ul>
        </nav>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
            <h5 class="text-muted">Geen audit logs gevonden</h5>
            <p class="text-muted">Er zijn nog geen activiteiten gelogd.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> Log Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <!-- Content wordt geladen via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Sluiten</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Filter functionaliteit
function filterLogs() {
    const actionFilter = document.getElementById('actionFilter').value.toLowerCase();
    const userFilter = document.getElementById('userFilter').value.toLowerCase();
    const dateFromFilter = document.getElementById('dateFromFilter').value;
    const dateToFilter = document.getElementById('dateToFilter').value;
    
    const table = document.getElementById('auditTable');
    const rows = table.getElementsByTagName('tr');
    let visibleCount = 0;
    
    for (let i = 1; i < rows.length; i++) { // Skip header row
        const row = rows[i];
        const action = row.getAttribute('data-action').toLowerCase();
        const user = row.getAttribute('data-user').toLowerCase();
        const date = row.getAttribute('data-date');
        
        let showRow = true;
        
        // Action filter
        if (actionFilter && action !== actionFilter) {
            showRow = false;
        }
        
        // User filter
        if (userFilter && !user.includes(userFilter)) {
            showRow = false;
        }
        
        // Date range filter
        if (dateFromFilter && date < dateFromFilter) {
            showRow = false;
        }
        if (dateToFilter && date > dateToFilter) {
            showRow = false;
        }
        
        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    }
    
    document.getElementById('logCount').textContent = visibleCount;
    updateStatistics();
}

function clearFilters() {
    document.getElementById('actionFilter').value = '';
    document.getElementById('userFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';
    filterLogs();
}

function refreshLogs() {
    location.reload();
}

function exportLogs() {
    // Hier zou je de logs kunnen exporteren naar CSV of JSON
    showToast('Export functionaliteit wordt geïmplementeerd...', 'info');
}

function clearOldLogs() {
    if (confirm('Weet je zeker dat je oude logs (ouder dan 30 dagen) wilt verwijderen?')) {
        // Hier zou je een API call maken om oude logs te verwijderen
        showToast('Oude logs worden verwijderd...', 'warning');
        setTimeout(() => location.reload(), 2000);
    }
}

function updateStatistics() {
    const table = document.getElementById('auditTable');
    const rows = table.getElementsByTagName('tr');
    
    let todayCount = 0;
    let loginCount = 0;
    let profileCount = 0;
    const today = new Date().toISOString().split('T')[0];
    
    for (let i = 1; i < rows.length; i++) {
        if (rows[i].style.display !== 'none') {
            const date = rows[i].getAttribute('data-date');
            const action = rows[i].getAttribute('data-action');
            
            if (date === today) todayCount++;
            if (action === 'login') loginCount++;
            if (action.includes('profile')) profileCount++;
        }
    }
    
    document.getElementById('todayLogs').textContent = todayCount;
    document.getElementById('loginCount').textContent = loginCount;
    document.getElementById('profileCount').textContent = profileCount;
}

function showLogDetails(logData) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Timestamp:</h6>
                <p>${logData.timestamp}</p>
                
                <h6>Gebruiker:</h6>
                <p><span class="badge bg-info">${logData.user}</span></p>
                
                <h6>Actie:</h6>
                <p><span class="badge bg-primary">${logData.action}</span></p>
            </div>
            <div class="col-md-6">
                <h6>IP Adres:</h6>
                <p><code>${logData.ip || 'N/A'}</code></p>
                
                <h6>Status:</h6>
                <p><span class="badge bg-success">${logData.status}</span></p>
                
                <h6>Details:</h6>
                <p>${logData.details}</p>
            </div>
        </div>
        
        <hr>
        
        <h6>User Agent:</h6>
        <p><small class="text-muted">${logData.userAgent || 'N/A'}</small></p>
    `;
    
    document.getElementById('logDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('logDetailsModal')).show();
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Add click event to table rows for details
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('auditTable');
    if (table) {
        const rows = table.getElementsByTagName('tr');
        for (let i = 1; i < rows.length; i++) {
            rows[i].style.cursor = 'pointer';
            rows[i].addEventListener('click', function() {
                const cells = this.getElementsByTagName('td');
                const logData = {
                    timestamp: cells[0].textContent.trim(),
                    user: cells[1].textContent.trim(),
                    action: cells[2].textContent.trim(),
                    details: cells[3].textContent.trim(),
                    ip: cells[4].textContent.trim(),
                    userAgent: cells[5].getAttribute('title') || cells[5].textContent.trim(),
                    status: cells[6].textContent.trim()
                };
                showLogDetails(logData);
            });
        }
    }
    
    updateStatistics();
});
</script>
{% endblock %}