# General ProGuard rules for the CipherCell VPN app

# Keep the application class and its members
-keep class com.ciphercell.vpn.** { *; }

# Keep VPN Service related classes
-keep class android.net.VpnService
-keep class * extends android.net.VpnService

# Keep WireGuard related classes
-keep class com.wireguard.android.** { *; }
-keep class com.wireguard.config.** { *; }

# Keep Retrofit and OkHttp classes
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-keep class retrofit2.** { *; }
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# Keep GSON related classes
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep BouncyCastle and SpongyCastle crypto libraries
-keep class org.bouncycastle.** { *; }
-keep class org.spongycastle.** { *; }
-dontwarn org.bouncycastle.**
-dontwarn org.spongycastle.**

# Keep Android framework classes
-keep class android.** { *; }
-keep interface android.** { *; }
-dontwarn android.**

# Keep JavaX crypto classes
-keep class javax.crypto.** { *; }
-dontwarn javax.crypto.**

# Keep Material Design components
-keep class com.google.android.material.** { *; }
-dontwarn com.google.android.material.**

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep parcelable classes
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Remove logging in release
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
}

# Keep Timber logging library configuration
-dontwarn org.jetbrains.annotations.**
-keep class timber.log.Timber { *; }

# Keep custom exceptions
-keep public class * extends java.lang.Exception

# Remove debugging info
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# Keep important resource files
-keep class **.R$* {
    public static <fields>;
}