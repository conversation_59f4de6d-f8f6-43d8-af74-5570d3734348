package com.ciphercell.vpn;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.MapView;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.switchmaterial.SwitchMaterial;

import java.util.HashMap;
import java.util.Map;

public class MapFragment extends Fragment implements OnMapReadyCallback {
    private MapView mapView;
    private GoogleMap googleMap;
    private TextView connectionStatus;
    private TextView imsiInfo;
    private Chip statusChip;
    private ChipGroup vpnChipGroup;
    private MaterialButton connectButton;
    private MaterialButton rotateButton;
    private SwitchMaterial autoRotateSwitch;
    
    private VPNConfigManager configManager;
    private IMSIManager imsiManager;
    private boolean isConnected = false;
    
    // VPN Server Locations
    private final Map<String, Map<String, LatLng>> vpnLocations = new HashMap<String, Map<String, LatLng>>() {{
        // WireGuard Servers
        put("WireGuard", new HashMap<String, LatLng>() {{
            put("US", new LatLng(37.7749, -122.4194)); // San Francisco
            put("EU", new LatLng(52.3676, 4.9041));   // Amsterdam
            put("ASIA", new LatLng(35.6762, 139.6503)); // Tokyo
        }});
        
        // NordVPN Servers
        put("NordVPN", new HashMap<String, LatLng>() {{
            put("US", new LatLng(40.7128, -74.0060));  // New York
            put("EU", new LatLng(48.8566, 2.3522));   // Paris
            put("ASIA", new LatLng(1.3521, 103.8198));  // Singapore
        }});
        
        // Mullvad Servers
        put("Mullvad", new HashMap<String, LatLng>() {{
            put("US", new LatLng(34.0522, -118.2437)); // Los Angeles
            put("EU", new LatLng(51.5074, -0.1278));   // London
            put("ASIA", new LatLng(22.3193, 114.1694)); // Hong Kong
        }});
    }};
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_map, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initializeViews(view);
        setupMapView(savedInstanceState);
        setupClickListeners();
        setupManagers();
    }
    
    private void initializeViews(View view) {
        mapView = view.findViewById(R.id.map_view);
        connectionStatus = view.findViewById(R.id.connection_status);
        imsiInfo = view.findViewById(R.id.imsi_info);
        statusChip = view.findViewById(R.id.status_chip);
        vpnChipGroup = view.findViewById(R.id.vpn_chip_group);
        connectButton = view.findViewById(R.id.connect_button);
        rotateButton = view.findViewById(R.id.rotate_button);
        autoRotateSwitch = view.findViewById(R.id.auto_rotate_switch);
    }
    
    private void setupMapView(Bundle savedInstanceState) {
        mapView.onCreate(savedInstanceState);
        mapView.getMapAsync(this);
    }
    
    private void setupClickListeners() {
        connectButton.setOnClickListener(v -> handleConnect());
        rotateButton.setOnClickListener(v -> handleRotate());
        
        vpnChipGroup.setOnCheckedChangeListener((group, checkedId) -> {
            updateMapMarkers();
        });
        
        autoRotateSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            handleAutoRotate(isChecked);
        });
    }
    
    private void setupManagers() {
        configManager = new VPNConfigManager(requireContext());
        imsiManager = new IMSIManager(requireContext());
    }
    
    @Override
    public void onMapReady(GoogleMap map) {
        googleMap = map;
        googleMap.setMapType(GoogleMap.MAP_TYPE_NORMAL);
        googleMap.getUiSettings().setZoomControlsEnabled(true);
        
        // Initial map setup
        updateMapMarkers();
        
        // Center map on US by default
        LatLng defaultLocation = vpnLocations.get("WireGuard").get("US");
        googleMap.moveCamera(CameraUpdateFactory.newLatLngZoom(defaultLocation, 2f));
    }
    
    private void updateMapMarkers() {
        if (googleMap == null) return;
        
        googleMap.clear();
        String selectedVpn = getSelectedVpn();
        Map<String, LatLng> locations = vpnLocations.get(selectedVpn);
        
        if (locations != null) {
            for (Map.Entry<String, LatLng> entry : locations.entrySet()) {
                googleMap.addMarker(new MarkerOptions()
                    .position(entry.getValue())
                    .title(selectedVpn + " - " + entry.getKey()));
            }
        }
    }
    
    private void handleConnect() {
        String selectedVpn = getSelectedVpn();
        if (!configManager.hasConfig(selectedVpn)) {
            Toast.makeText(requireContext(), R.string.error_configuration,
                Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!isConnected) {
            // Start VPN connection
            ((MainActivity) requireActivity()).startVpnConnection(selectedVpn, "US");
            isConnected = true;
        } else {
            // Disconnect VPN
            ((MainActivity) requireActivity()).disconnectVpn();
            isConnected = false;
        }
        
        updateUI();
    }
    
    private void handleRotate() {
        if (isConnected) {
            String selectedVpn = getSelectedVpn();
            ((MainActivity) requireActivity()).rotateConnection(selectedVpn);
            updateUI();
        }
    }
    
    private void handleAutoRotate(boolean enabled) {
        if (isConnected) {
            ((MainActivity) requireActivity()).setAutoRotation(enabled);
        } else {
            autoRotateSwitch.setChecked(false);
        }
    }
    
    private String getSelectedVpn() {
        int selectedId = vpnChipGroup.getCheckedChipId();
        if (selectedId == R.id.wireguard_chip) return "WireGuard";
        if (selectedId == R.id.nordvpn_chip) return "NordVPN";
        if (selectedId == R.id.mullvad_chip) return "Mullvad";
        return "WireGuard"; // Default
    }
    
    private void updateUI() {
        // Update connection status
        connectionStatus.setText(isConnected ?
            getString(R.string.status_connected) :
            getString(R.string.status_disconnected));
        
        // Update status chip
        statusChip.setText(isConnected ?
            getString(R.string.status_connected) :
            getString(R.string.status_disconnected));
        statusChip.setChipBackgroundColorResource(isConnected ?
            R.color.status_connected :
            R.color.status_disconnected);
        
        // Update IMSI info
        String currentImsi = imsiManager.getCurrentIMSI();
        imsiInfo.setText(currentImsi != null ?
            getString(R.string.imsi_current, currentImsi) :
            getString(R.string.imsi_not_generated));
        
        // Update button text
        connectButton.setText(isConnected ?
            getString(R.string.action_disconnect) :
            getString(R.string.action_connect));
        
        // Update button states
        rotateButton.setEnabled(isConnected);
        autoRotateSwitch.setEnabled(isConnected);
    }
    
    @Override
    public void onResume() {
        super.onResume();
        mapView.onResume();
    }
    
    @Override
    public void onPause() {
        super.onPause();
        mapView.onPause();
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        mapView.onDestroy();
    }
    
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        mapView.onLowMemory();
    }
    
    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        mapView.onSaveInstanceState(outState);
    }
}