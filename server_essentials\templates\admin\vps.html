{% extends "admin/base.html" %}

{% block title %}VPS Management{% endblock %}
{% block header %}VPS Management{% endblock %}

{% block content %}
<!-- Server Status Overview -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <!-- CPU Usage -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-500 text-sm">CPU Usage</p>
                <p class="text-2xl font-semibold">{{ cpu_usage|default('0') }}%</p>
            </div>
            <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                <i class="fas fa-microchip text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 h-2 bg-gray-200 rounded-full">
            <div class="h-2 bg-blue-500 rounded-full" style="width: {{ cpu_usage|default('0') }}%"></div>
        </div>
    </div>

    <!-- Memory Usage -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-500 text-sm">Memory Usage</p>
                <p class="text-2xl font-semibold">{{ memory_usage|default('0') }}%</p>
            </div>
            <div class="p-3 rounded-full bg-green-100 text-green-500">
                <i class="fas fa-memory text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 h-2 bg-gray-200 rounded-full">
            <div class="h-2 bg-green-500 rounded-full" style="width: {{ memory_usage|default('0') }}%"></div>
        </div>
    </div>

    <!-- Disk Usage -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-500 text-sm">Disk Usage</p>
                <p class="text-2xl font-semibold">{{ disk_usage|default('0') }}%</p>
            </div>
            <div class="p-3 rounded-full bg-purple-100 text-purple-500">
                <i class="fas fa-hdd text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 h-2 bg-gray-200 rounded-full">
            <div class="h-2 bg-purple-500 rounded-full" style="width: {{ disk_usage|default('0') }}%"></div>
        </div>
    </div>

    <!-- Network Traffic -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-500 text-sm">Network Traffic</p>
                <p class="text-2xl font-semibold">{{ network_traffic|default('0 MB/s') }}</p>
            </div>
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                <i class="fas fa-network-wired text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 flex justify-between text-sm">
            <span class="text-green-500">↑ {{ upload_speed|default('0 MB/s') }}</span>
            <span class="text-blue-500">↓ {{ download_speed|default('0 MB/s') }}</span>
        </div>
    </div>
</div>

<!-- Server Controls -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Server Controls</h2>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <button onclick="connectSSH()" class="flex items-center justify-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <i class="fas fa-terminal text-blue-500 text-xl mr-3"></i>
            <span>SSH Console</span>
        </button>
        <button onclick="showServiceManager()" class="flex items-center justify-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
            <i class="fas fa-cogs text-green-500 text-xl mr-3"></i>
            <span>Services</span>
        </button>
        <button onclick="showFirewall()" class="flex items-center justify-center p-4 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
            <i class="fas fa-shield-alt text-red-500 text-xl mr-3"></i>
            <span>Firewall</span>
        </button>
        <button onclick="showBackups()" class="flex items-center justify-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <i class="fas fa-backup text-purple-500 text-xl mr-3"></i>
            <span>Backups</span>
        </button>
    </div>
</div>

<!-- Service Status -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Service Status</h2>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uptime</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for service in services|default([]) %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ service.name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            {% if service.status == 'running' %}bg-green-100 text-green-800
                            {% else %}bg-red-100 text-red-800{% endif %}">
                            {{ service.status }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ service.uptime }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button onclick="restartService('{{ service.name }}')" class="text-yellow-600 hover:text-yellow-900">
                            <i class="fas fa-sync"></i>
                        </button>
                        {% if service.status == 'running' %}
                        <button onclick="stopService('{{ service.name }}')" class="text-red-600 hover:text-red-900">
                            <i class="fas fa-stop"></i>
                        </button>
                        {% else %}
                        <button onclick="startService('{{ service.name }}')" class="text-green-600 hover:text-green-900">
                            <i class="fas fa-play"></i>
                        </button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- System Logs -->
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">System Logs</h2>
        <select id="logFilter" class="border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="all">All Logs</option>
            <option value="error">Errors</option>
            <option value="warning">Warnings</option>
            <option value="info">Info</option>
        </select>
    </div>
    <div class="bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
        {% for log in system_logs|default([]) %}
        <div class="text-gray-300">{{ log }}</div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function connectSSH() {
    // Implement SSH connection logic
}

function showServiceManager() {
    // Implement service manager modal
}

function showFirewall() {
    // Implement firewall settings modal
}

function showBackups() {
    // Implement backup management modal
}

function restartService(serviceName) {
    if (confirm(`Are you sure you want to restart ${serviceName}?`)) {
        // Implement service restart logic
    }
}

function stopService(serviceName) {
    if (confirm(`Are you sure you want to stop ${serviceName}?`)) {
        // Implement service stop logic
    }
}

function startService(serviceName) {
    // Implement service start logic
}

// Add real-time monitoring updates
setInterval(() => {
    // Implement status update logic
}, 5000);
</script>
{% endblock %}