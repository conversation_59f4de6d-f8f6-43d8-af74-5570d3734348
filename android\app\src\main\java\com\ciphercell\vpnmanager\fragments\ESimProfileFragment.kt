package com.ciphercell.vpnmanager.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.ciphercell.vpnmanager.R
import com.ciphercell.vpnmanager.databinding.FragmentEsimProfileBinding
import com.ciphercell.vpnmanager.esim.ESimManager
import com.ciphercell.vpnmanager.esim.ESimStatus
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ESimProfileFragment : Fragment() {

    private var _binding: FragmentEsimProfileBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var eSimManager: ESimManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentEsimProfileBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        loadESimProfiles()
    }

    private fun setupUI() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            loadESimProfiles()
        }

        binding.fabAddProfile.setOnClickListener {
            showAddProfileDialog()
        }
    }

    private fun loadESimProfiles() {
        binding.swipeRefreshLayout.isRefreshing = true
        
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                when (val status = eSimManager.getESimStatus()) {
                    is ESimStatus.NotSupported -> {
                        binding.statusText.text = getString(R.string.esim_not_supported)
                        binding.profilesGroup.visibility = View.GONE
                    }
                    is ESimStatus.Inactive -> {
                        binding.statusText.text = getString(R.string.no_esim_profiles)
                        binding.profilesGroup.visibility = View.GONE
                    }
                    is ESimStatus.Active -> {
                        val profiles = eSimManager.getActiveESimProfiles()
                        if (profiles.isEmpty()) {
                            binding.statusText.text = getString(R.string.no_esim_profiles)
                            binding.profilesGroup.visibility = View.GONE
                        } else {
                            binding.statusText.visibility = View.GONE
                            binding.profilesGroup.visibility = View.VISIBLE
                            // TODO: Update RecyclerView with profiles
                        }
                    }
                }
            } catch (e: Exception) {
                binding.statusText.text = getString(R.string.error_loading_profiles, e.message)
                binding.profilesGroup.visibility = View.GONE
            } finally {
                binding.swipeRefreshLayout.isRefreshing = false
            }
        }
    }

    private fun showAddProfileDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.add_esim_profile)
            .setView(R.layout.dialog_add_esim_profile)
            .setPositiveButton(R.string.add) { dialog, _ ->
                // TODO: Handle adding new eSIM profile
                dialog.dismiss()
            }
            .setNegativeButton(R.string.cancel) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
