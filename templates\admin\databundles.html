{% extends "admin/base.html" %}

{% block header %}Data Bundles Management{% endblock %}

{% block content %}
<div class="p-6">
    <!-- Action Buttons -->
    <div class="mb-6 flex justify-between items-center">
        <div class="flex space-x-4">
            <button onclick="openAddModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                <i class="fas fa-plus mr-2"></i>Add Bundle
            </button>
            <button onclick="openImportModal()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                <i class="fas fa-file-import mr-2"></i>Import Bundles
            </button>
        </div>
        <div class="flex space-x-4">
            <input type="text" id="searchInput" placeholder="Search bundles..." class="border rounded px-4 py-2">
            <select id="regionFilter" class="border rounded px-4 py-2">
                <option value="">All Regions</option>
                <option value="NL">Netherlands</option>
                <option value="EU">European Union</option>
                <option value="WW">Worldwide</option>
            </select>
            <select id="durationFilter" class="border rounded px-4 py-2">
                <option value="">All Durations</option>
                <option value="1">1 Month</option>
                <option value="3">3 Months</option>
                <option value="6">6 Months</option>
            </select>
        </div>
    </div>
        <!-- Bundles Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fair Use (GB)</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="bundlesTableBody">
                <!-- Bundle rows will be dynamically inserted here -->
            </tbody>
        </table>
    </div>
</div>

<!-- Add/Edit Bundle Modal -->
<div id="bundleModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4" id="modalTitle">Add New Bundle</h3>
            <form id="bundleForm">
                <input type="hidden" id="bundleId">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Name</label>
                    <input type="text" id="bundleName" class="border rounded w-full py-2 px-3" required>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Region</label>
                    <select id="bundleRegion" class="border rounded w-full py-2 px-3" required>
                        <option value="NL">Netherlands</option>
                        <option value="EU">European Union</option>
                        <option value="WW">Worldwide</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Duration (Months)</label>
                    <select id="bundleDuration" class="border rounded w-full py-2 px-3" required>
                        <option value="1">1 Month</option>
                        <option value="3">3 Months</option>
                        <option value="6">6 Months</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Data Type</label>
                    <select id="bundleDataType" class="border rounded w-full py-2 px-3" required onchange="toggleFairUseInput()">
                        <option value="limited">Limited</option>
                        <option value="unlimited">Unlimited</option>
                    </select>
                </div>
                <div class="mb-4" id="dataAmountContainer">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Data Amount (GB)</label>
                    <input type="number" id="bundleDataAmount" class="border rounded w-full py-2 px-3" min="1">
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Fair Use Policy (GB)</label>
                    <input type="number" id="bundleFairUse" class="border rounded w-full py-2 px-3" min="0" step="0.1" required>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Price</label>
                    <input type="number" id="bundlePrice" class="border rounded w-full py-2 px-3" min="0" step="0.01" required>
                </div>
                <div class="flex justify-end space-x-4">
                    <button type="button" onclick="closeModal()" class="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded">Cancel</button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div id="importModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Import Bundles</h3>
            <form id="importForm">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">CSV File</label>
                    <input type="file" id="csvFile" accept=".csv" class="border rounded w-full py-2 px-3" required>
                </div>
                <div class="flex justify-end space-x-4">
                    <button type="button" onclick="closeImportModal()" class="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded">Cancel</button>
                    <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">Import</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleFairUseInput() {
    const dataType = document.getElementById('bundleDataType').value;
    const dataAmountContainer = document.getElementById('dataAmountContainer');
    if (dataType === 'unlimited') {
        dataAmountContainer.style.display = 'none';
        document.getElementById('bundleDataAmount').value = -1;
    } else {
        dataAmountContainer.style.display = 'block';
        document.getElementById('bundleDataAmount').value = '';
    }
}

function openAddModal() {
    document.getElementById('modalTitle').textContent = 'Add New Bundle';
    document.getElementById('bundleForm').reset();
    document.getElementById('bundleId').value = '';
    document.getElementById('bundleModal').classList.remove('hidden');
}

function openEditModal(bundle) {
    document.getElementById('modalTitle').textContent = 'Edit Bundle';
    document.getElementById('bundleId').value = bundle.id;
    document.getElementById('bundleName').value = bundle.name;
    document.getElementById('bundleRegion').value = bundle.region;
    document.getElementById('bundleDuration').value = bundle.duration;
    document.getElementById('bundleDataType').value = bundle.data_amount === -1 ? 'unlimited' : 'limited';
    document.getElementById('bundleDataAmount').value = bundle.data_amount === -1 ? '' : bundle.data_amount;
    document.getElementById('bundleFairUse').value = bundle.fair_use;
    document.getElementById('bundlePrice').value = bundle.price;
    toggleFairUseInput();
    document.getElementById('bundleModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('bundleModal').classList.add('hidden');
}

function openImportModal() {
    document.getElementById('importModal').classList.remove('hidden');
}

function closeImportModal() {
    document.getElementById('importModal').classList.add('hidden');
}

function loadBundles() {
    console.log('Loading bundles...');
    fetch('/api/databundles')
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(bundles => {
            console.log('Received bundles:', bundles);
            const tbody = document.getElementById('bundlesTableBody');
            tbody.innerHTML = '';
            bundles.forEach(bundle => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">${bundle.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${bundle.region}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${bundle.duration} Month${bundle.duration > 1 ? 's' : ''}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${bundle.data_amount === -1 ? 'Unlimited' : bundle.data_amount + ' GB'}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${bundle.fair_use} GB</td>
                    <td class="px-6 py-4 whitespace-nowrap">€${bundle.price.toFixed(2)}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${bundle.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${bundle.active ? 'Active' : 'Inactive'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick='openEditModal(${JSON.stringify(bundle)})' class="text-indigo-600 hover:text-indigo-900 mr-4">Edit</button>
                        <button onclick="deleteBundle(${bundle.id})" class="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error loading bundles:', error);
        });
}

document.getElementById('bundleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const bundleData = {
        name: document.getElementById('bundleName').value,
        region: document.getElementById('bundleRegion').value,
        duration: parseInt(document.getElementById('bundleDuration').value),
        data_amount: document.getElementById('bundleDataType').value === 'unlimited' ? -1 : parseFloat(document.getElementById('bundleDataAmount').value),
        fair_use: parseFloat(document.getElementById('bundleFairUse').value),
        price: parseFloat(document.getElementById('bundlePrice').value)
    };

    const bundleId = document.getElementById('bundleId').value;
    const method = bundleId ? 'PUT' : 'POST';
    const url = bundleId ? `/api/databundles/${bundleId}` : '/api/databundles';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(bundleData)
    })
    .then(response => response.json())
    .then(() => {
        closeModal();
        loadBundles();
    });
});

document.getElementById('importForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const file = document.getElementById('csvFile').files[0];
    const formData = new FormData();
    formData.append('file', file);

    fetch('/api/databundles/import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(() => {
        closeImportModal();
        loadBundles();
    });
});

function deleteBundle(id) {
    if (confirm('Are you sure you want to delete this bundle?')) {
        fetch(`/api/databundles/${id}`, {
            method: 'DELETE'
        })
        .then(() => loadBundles());
    }
}

// Search and filter functionality
document.getElementById('searchInput').addEventListener('input', filterBundles);
document.getElementById('regionFilter').addEventListener('change', filterBundles);
document.getElementById('durationFilter').addEventListener('change', filterBundles);

function filterBundles() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const regionFilter = document.getElementById('regionFilter').value;
    const durationFilter = document.getElementById('durationFilter').value;

    const rows = document.getElementById('bundlesTableBody').getElementsByTagName('tr');
    
    Array.from(rows).forEach(row => {
        const name = row.cells[0].textContent.toLowerCase();
        const region = row.cells[1].textContent;
        const duration = row.cells[2].textContent.split(' ')[0];

        const matchesSearch = name.includes(searchTerm);
        const matchesRegion = !regionFilter || region === regionFilter;
        const matchesDuration = !durationFilter || duration === durationFilter;

        row.style.display = matchesSearch && matchesRegion && matchesDuration ? '' : 'none';
    });
}

// Initial load
loadBundles();
</script>
{% endblock %}

    <!-- Search and Filter -->
    <div class="mb-4 flex gap-4">
        <input type="text" id="searchInput" placeholder="Search bundles..." class="p-2 border rounded flex-grow">
        <select id="regionFilter" class="p-2 border rounded">
            <option value="">All Regions</option>
            <option value="NL">Netherlands</option>
            <option value="EU">Europe</option>
            <option value="WW">Worldwide</option>
        </select>
        <select id="durationFilter" class="p-2 border rounded">
            <option value="">All Durations</option>
            <option value="30">1 Month</option>
            <option value="90">3 Months</option>
            <option value="180">6 Months</option>
        </select>
    </div>

    <!-- Data Bundles Table -->
    <div class="bg-white shadow-md rounded my-6 overflow-x-auto">
        <table class="min-w-full table-auto">
            <thead>
                <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left">Name</th>
                    <th class="py-3 px-6 text-left">Region</th>
                    <th class="py-3 px-6 text-left">Duration</th>
                    <th class="py-3 px-6 text-left">Data Amount</th>
                    <th class="py-3 px-6 text-left">Price</th>
                    <th class="py-3 px-6 text-center">Actions</th>
                </tr>
            </thead>
            <tbody id="bundleTable" class="text-gray-600 text-sm">
                <!-- Table content will be populated by JavaScript -->
            </tbody>
        </table>
    </div>

    <!-- Add/Edit Modal -->
    <div id="bundleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4" id="modalTitle">Add New Bundle</h3>
                <form id="bundleForm" class="space-y-4">
                    <input type="hidden" id="bundleId">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" id="bundleName" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Region</label>
                        <select id="bundleRegion" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="NL">Netherlands</option>
                            <option value="EU">Europe</option>
                            <option value="WW">Worldwide</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Duration (Days)</label>
                        <select id="bundleDuration" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="30">1 Month (30 Days)</option>
                            <option value="90">3 Months (90 Days)</option>
                            <option value="180">6 Months (180 Days)</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Data Amount (GB)</label>
                        <input type="number" id="bundleDataAmount" value="-1" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <p class="text-sm text-gray-500 mt-1">Enter -1 for unlimited data</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Price (€)</label>
                        <input type="number" id="bundlePrice" step="0.01" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Fair Use Policy (GB/day)</label>
                        <input type="number" id="bundleFUP" step="0.1" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <p class="text-sm text-gray-500 mt-1">Daily data usage limit for unlimited plans</p>
                    </div>
                    <div class="flex justify-end gap-3">
                        <button type="button" onclick="closeModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Cancel</button>
                        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let bundles = [];

// Fetch and display data bundles
async function fetchBundles() {
    try {
        const response = await fetch('/api/databundles');
        bundles = await response.json();
        displayBundles();
    } catch (error) {
        console.error('Error fetching bundles:', error);
    }
}

// Display bundles in table
function displayBundles() {
    const tbody = document.getElementById('bundleTable');
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const regionFilter = document.getElementById('regionFilter').value;
    const durationFilter = document.getElementById('durationFilter').value;

    const filteredBundles = bundles.filter(bundle => {
        const matchesSearch = bundle.name.toLowerCase().includes(searchTerm);
        const matchesRegion = !regionFilter || bundle.region === regionFilter;
        const matchesDuration = !durationFilter || bundle.duration.toString() === durationFilter;
        return matchesSearch && matchesRegion && matchesDuration;
    });

    tbody.innerHTML = filteredBundles.map(bundle => `
        <tr class="border-b border-gray-200 hover:bg-gray-100">
            <td class="py-3 px-6 text-left">${bundle.name}</td>
            <td class="py-3 px-6 text-left">${bundle.region}</td>
            <td class="py-3 px-6 text-left">${bundle.duration} days</td>
            <td class="py-3 px-6 text-left">${bundle.data_amount === -1 ? 'Unlimited' : bundle.data_amount + ' GB'}</td>
            <td class="py-3 px-6 text-left">€${bundle.price.toFixed(2)}</td>
            <td class="py-3 px-6 text-center">
                <button onclick="editBundle(${bundle.id})" class="text-blue-600 hover:text-blue-900 mr-3">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteBundle(${bundle.id})" class="text-red-600 hover:text-red-900">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// Modal functions
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'Add New Bundle';
    document.getElementById('bundleForm').reset();
    document.getElementById('bundleId').value = '';
    document.getElementById('bundleModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('bundleModal').classList.add('hidden');
}

// Edit bundle
async function editBundle(id) {
    const bundle = bundles.find(b => b.id === id);
    if (bundle) {
        document.getElementById('modalTitle').textContent = 'Edit Bundle';
        document.getElementById('bundleId').value = bundle.id;
        document.getElementById('bundleName').value = bundle.name;
        document.getElementById('bundleRegion').value = bundle.region;
        document.getElementById('bundleDuration').value = bundle.duration;
        document.getElementById('bundleDataAmount').value = bundle.data_amount;
        document.getElementById('bundlePrice').value = bundle.price;
        document.getElementById('bundleFUP').value = bundle.fair_use;
        document.getElementById('bundleModal').classList.remove('hidden');
    }
}

// Delete bundle
async function deleteBundle(id) {
    if (confirm('Are you sure you want to delete this bundle?')) {
        try {
            const response = await fetch(`/api/databundles/${id}`, {
                method: 'DELETE'
            });
            if (response.ok) {
                await fetchBundles();
            } else {
                alert('Failed to delete bundle');
            }
        } catch (error) {
            console.error('Error deleting bundle:', error);
        }
    }
}

// Form submission
document.getElementById('bundleForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const bundleId = document.getElementById('bundleId').value;
    const bundleData = {
        name: document.getElementById('bundleName').value,
        region: document.getElementById('bundleRegion').value,
        duration: parseInt(document.getElementById('bundleDuration').value),
        data_amount: parseInt(document.getElementById('bundleDataAmount').value),
        price: parseFloat(document.getElementById('bundlePrice').value),
        fair_use: parseFloat(document.getElementById('bundleFUP').value)
    };

    try {
        const url = bundleId ? `/api/databundles/${bundleId}` : '/api/databundles';
        const method = bundleId ? 'PUT' : 'POST';
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(bundleData)
        });

        if (response.ok) {
            closeModal();
            await fetchBundles();
        } else {
            alert('Failed to save bundle');
        }
    } catch (error) {
        console.error('Error saving bundle:', error);
    }
});

// Search and filter event listeners
document.getElementById('searchInput').addEventListener('input', displayBundles);
document.getElementById('regionFilter').addEventListener('change', displayBundles);
document.getElementById('durationFilter').addEventListener('change', displayBundles);

// Initial load
fetchBundles();
</script>
{% endblock %}