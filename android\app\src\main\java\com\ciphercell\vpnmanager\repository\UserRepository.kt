package com.ciphercell.vpnmanager.repository

import com.ciphercell.vpnmanager.network.ApiService
import com.ciphercell.vpnmanager.network.EsimActivationRequest
import com.ciphercell.vpnmanager.network.LoginRequest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for handling user-related data operations.
 *
 * This class abstracts the data source (network) and provides a clean API for the UI/ViewModel
 * to interact with user and eSIM data.
 *
 * @param apiService The Retrofit service for making network calls.
 */
@Singleton
class UserRepository @Inject constructor(private val apiService: ApiService) {

    /**
     * Authenticates the user with the backend.
     *
     * @param email The user's email.
     * @param password The user's password.
     * @return The login response from the server.
     */
    suspend fun loginUser(email: String, password: String) = apiService.loginUser(
        LoginRequest(email = email, pass = password)
    )

    /**
     * Activates an eSIM profile using an activation code.
     *
     * @param authToken The user's authorization token.
     * @param activationCode The eSIM activation code.
     * @return The activation response from the server.
     */
    suspend fun activateEsim(authToken: String, activationCode: String) = apiService.activateEsim(
        authToken = "Bearer $authToken",
        activationRequest = EsimActivationRequest(activationCode = activationCode)
    )

    /**
     * Fetches the WireGuard VPN configuration from the backend.
     *
     * @param authToken The user's authorization token.
     * @return The VPN configuration response from the server.
     */
    suspend fun getVpnConfig(authToken: String) = apiService.getVpnConfig(
        authToken = "Bearer $authToken"
    )
}
