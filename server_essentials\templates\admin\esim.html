{% extends "admin/base.html" %}

{% block title %}eSIM Management{% endblock %}
{% block header %}eSIM Management{% endblock %}

{% block content %}
<!-- Action Buttons -->
<div class="mb-6 flex justify-between items-center">
    <div class="flex space-x-4">
        <button onclick="showGenerateModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-plus-circle mr-2"></i> Generate New eSIM
        </button>
        <button onclick="showImportModal()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-file-import mr-2"></i> Import eSIMs
        </button>
    </div>
    <div class="flex items-center space-x-4">
        <div class="relative">
            <input type="text" id="search" placeholder="Search eSIMs..." 
                   class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
        </div>
        <select id="filter" class="border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="revoked">Revoked</option>
            <option value="expired">Expired</option>
        </select>
    </div>
</div>

<!-- eSIM List -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ICCID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profile Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Security</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Access</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for esim in esims|default([]) %}
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ esim.id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ esim.iccid }}
                    {% if esim.imsi_numbers %}
                    <button onclick="toggleIMSI('{{ esim.id }}')" class="ml-2 text-blue-500 hover:text-blue-700">
                        <i class="fas fa-list"></i>
                    </button>
                    <div id="imsi-{{ esim.id }}" class="hidden mt-2 bg-gray-50 p-2 rounded-md">
                        <div class="text-xs font-medium text-gray-500 mb-1">IMSI Numbers:</div>
                        {% for imsi in esim.imsi_numbers %}
                        <div class="text-xs text-gray-600">{{ imsi }}</div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        {% if esim.profile_type == 'premium' %}bg-purple-100 text-purple-800
                        {% else %}bg-blue-100 text-blue-800{% endif %}">
                        {{ esim.profile_type }}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if esim.status == 'active' %}bg-green-100 text-green-800
                        {% elif esim.status == 'inactive' %}bg-yellow-100 text-yellow-800
                        {% elif esim.status == 'revoked' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ esim.status }}
                        {% if esim.revocation_reason %}
                        <span class="ml-1 cursor-help" title="{{ esim.revocation_reason }}">ⓘ</span>
                        {% endif %}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-2">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                            {% if esim.encryption_version >= 2 %}bg-green-100 text-green-800
                            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                            v{{ esim.encryption_version }}
                        </span>
                        <span class="text-xs text-gray-500" title="Access Count">{{ esim.access_count }} uses</span>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ esim.created_date }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ esim.expiry_date }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ esim.last_access_date or 'Never' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button onclick="showQRCode('{{ esim.id }}')" class="text-blue-600 hover:text-blue-900" title="Show QR Code">
                        <i class="fas fa-qrcode"></i>
                    </button>
                    <button onclick="showEditModal('{{ esim.id }}')" class="text-green-600 hover:text-green-900" title="Edit eSIM">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="confirmDelete('{{ esim.id }}')" class="text-red-600 hover:text-red-900" title="Delete eSIM">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Generate eSIM Modal -->
<div id="generateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Generate New eSIM</h3>
            <div class="mt-2 px-7 py-3">
                <form id="generateForm" class="space-y-4">
                    <div class="text-left">
                        <label class="block text-sm font-medium text-gray-700">Profile Type</label>
                        <select name="profile_type" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="standard">Standard (1 IMSI)</option>
                            <option value="premium">Premium (5 IMSIs)</option>
                        </select>
                    </div>
                    <div class="text-left">
                        <label class="block text-sm font-medium text-gray-700">Validity (days)</label>
                        <input type="number" name="validity" required min="1" max="365" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <p class="mt-1 text-xs text-gray-500">Enter a value between 1 and 365 days</p>
                    </div>
                    <div class="text-left">
                        <label class="block text-sm font-medium text-gray-700">Security Level</label>
                        <select name="security_level" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="2">Enhanced (AES-256 + HMAC)</option>
                            <option value="1">Standard (AES-128)</option>
                        </select>
                    </div>
                    <div class="text-left">
                        <label class="block text-sm font-medium text-gray-700">Access Restrictions</label>
                        <div class="mt-2 space-y-2">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="geo_restriction" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Enable Geographic Restrictions</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="device_binding" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Enable Device Binding</span>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="hideGenerateModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">Cancel</button>
                        <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">Generate</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Import eSIMs</h3>
            <div class="mt-2 px-7 py-3">
                <form id="importForm" class="space-y-4">
                    <div class="text-left">
                        <label class="block text-sm font-medium text-gray-700">CSV File</label>
                        <input type="file" accept=".csv" class="mt-1 block w-full">
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="hideImportModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">Cancel</button>
                        <button type="submit" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">Import</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleIMSI(id) {
    const imsiDiv = document.getElementById(`imsi-${id}`);
    if (imsiDiv.classList.contains('hidden')) {
        imsiDiv.classList.remove('hidden');
    } else {
        imsiDiv.classList.add('hidden');
    }
}

function showGenerateModal() {
    document.getElementById('generateModal').classList.remove('hidden');
}

function hideGenerateModal() {
    document.getElementById('generateModal').classList.add('hidden');
}

function showImportModal() {
    document.getElementById('importModal').classList.remove('hidden');
}

function hideImportModal() {
    document.getElementById('importModal').classList.add('hidden');
}

async function showQRCode(id) {
    try {
        const response = await fetch(`/api/esim/${id}/qr`);
        if (response.ok) {
            const data = await response.json();
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-white p-6 rounded-lg shadow-xl">
                    <h3 class="text-lg font-medium mb-4">eSIM QR Code</h3>
                    <div class="mb-4">
                        <img src="${data.qr_code}" alt="eSIM QR Code" class="mx-auto">
                    </div>
                    <div class="text-sm text-gray-500 mb-4">
                        <p>Security Version: v${data.encryption_version}</p>
                        <p>Access Count: ${data.access_count}</p>
                        <p>Last Access: ${data.last_access_date || 'Never'}</p>
                    </div>
                    <div class="flex justify-end">
                        <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                                class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
                            Close
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };
        } else {
            const data = await response.json();
            alert('Error: ' + (data.message || 'Failed to load QR code'));
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to load QR code');
    }
}

async function showEditModal(id) {
    try {
        const response = await fetch(`/api/esim/${id}`);
        if (response.ok) {
            const esim = await response.json();
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-white p-6 rounded-lg shadow-xl w-96">
                    <h3 class="text-lg font-medium mb-4">Edit eSIM Security Settings</h3>
                    <form id="editForm-${id}" class="space-y-4">
                        <div class="text-left">
                            <label class="block text-sm font-medium text-gray-700">Geographic Restrictions</label>
                            <select name="geo_restriction" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="enabled" ${esim.geo_restriction ? 'selected' : ''}>Enabled</option>
                                <option value="disabled" ${!esim.geo_restriction ? 'selected' : ''}>Disabled</option>
                            </select>
                        </div>
                        <div class="text-left">
                            <label class="block text-sm font-medium text-gray-700">Device Binding</label>
                            <select name="device_binding" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="enabled" ${esim.device_binding ? 'selected' : ''}>Enabled</option>
                                <option value="disabled" ${!esim.device_binding ? 'selected' : ''}>Disabled</option>
                            </select>
                        </div>
                        <div class="flex justify-end space-x-4 mt-6">
                            <button type="button" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" 
                                    class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">Cancel</button>
                            <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">Save Changes</button>
                        </div>
                    </form>
                </div>
            `;
            document.body.appendChild(modal);
            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };
            
            document.getElementById(`editForm-${id}`).onsubmit = async (e) => {
                e.preventDefault();
                try {
                    const formData = {
                        geo_restriction: e.target.geo_restriction.value === 'enabled',
                        device_binding: e.target.device_binding.value === 'enabled'
                    };
                    
                    const updateResponse = await fetch(`/api/esim/${id}/update`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    if (updateResponse.ok) {
                        location.reload();
                    } else {
                        const data = await updateResponse.json();
                        alert('Error: ' + (data.message || 'Failed to update eSIM'));
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Failed to update eSIM');
                }
            };
        } else {
            const data = await response.json();
            alert('Error: ' + (data.message || 'Failed to load eSIM data'));
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to load eSIM data');
    }
}

async function confirmDelete(id) {
    const reason = prompt('Please enter a reason for deletion/revocation:');
    if (reason) {
        try {
            const response = await fetch(`/api/esim/${id}/revoke`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ reason: reason })
            });
            
            if (response.ok) {
                location.reload();
            } else {
                const data = await response.json();
                alert('Error: ' + (data.message || 'Failed to revoke eSIM'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Failed to revoke eSIM');
        }
    }
}

// Handle eSIM generation form submission
document.getElementById('generateForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = {
        profile_type: this.profile_type.value,
        validity: parseInt(this.validity.value),
        security_level: parseInt(this.security_level.value),
        geo_restriction: this.geo_restriction.checked,
        device_binding: this.device_binding.checked
    };
    
    if (formData.validity < 1 || formData.validity > 365) {
        alert('Validity must be between 1 and 365 days');
        return;
    }
    
    try {
        const response = await fetch('/api/esim/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            // Show success message with IMSI numbers
            const imsiList = result.imsi_list.join('\n');
            alert(`eSIM generated successfully!\n\nProfile Type: ${formData.profile_type}\nICCID: ${result.esim.iccid}\n\nIMSI Numbers:\n${imsiList}`);
            
            // Reload the page to show the new eSIM
            window.location.reload();
        } else {
            alert('Error generating eSIM: ' + result.message);
        }
    } catch (error) {
        alert('Error generating eSIM: ' + error.message);
    }
    
    hideGenerateModal();
});
</script>
{% endblock %}