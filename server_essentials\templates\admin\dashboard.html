{% extends "admin/base.html" %}

{% block title %}Dashboard{% endblock %}
{% block header %}Dashboard{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <!-- eSIM Stats -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                <i class="fas fa-sim-card text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">Total eSIMs</p>
                <p class="text-2xl font-semibold">{{ esim_count|default(0) }}</p>
            </div>
        </div>
    </div>

    <!-- VPS Status -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-500">
                <i class="fas fa-server text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">VPS Status</p>
                <p class="text-2xl font-semibold text-green-500">Online</p>
            </div>
        </div>
    </div>

    <!-- Users -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-500">
                <i class="fas fa-users text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">Total Users</p>
                <p class="text-2xl font-semibold">{{ user_count|default(0) }}</p>
            </div>
        </div>
    </div>

    <!-- System Load -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                <i class="fas fa-microchip text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">System Load</p>
                <p class="text-2xl font-semibold">{{ system_load|default('0%') }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a href="{{ url_for('admin_esim') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <i class="fas fa-plus-circle text-blue-500 text-xl mr-3"></i>
            <span>Generate New eSIM</span>
        </a>
        <a href="{{ url_for('admin_vps') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
            <i class="fas fa-terminal text-green-500 text-xl mr-3"></i>
            <span>VPS Console</span>
        </a>
        <a href="#" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <i class="fas fa-chart-line text-purple-500 text-xl mr-3"></i>
            <span>View Reports</span>
        </a>
        <a href="#" class="flex items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
            <i class="fas fa-cog text-yellow-500 text-xl mr-3"></i>
            <span>Settings</span>
        </a>
    </div>
</div>

<!-- Recent Activity -->
<div class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4">Recent Activity</h2>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for activity in recent_activities|default([]) %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ activity.time }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ activity.action }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ activity.user }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            {{ activity.status }}
                        </span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Add any dashboard-specific JavaScript here
</script>
{% endblock %}