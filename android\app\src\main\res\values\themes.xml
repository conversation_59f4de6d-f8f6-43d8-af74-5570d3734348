<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme -->
    <style name="Theme.CipherCellVPN" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        
        <!-- Status bar color -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/on_error</item>
        
        <!-- Custom attributes -->
        <item name="materialCardViewStyle">@style/Widget.CipherCellVPN.CardView</item>
        <item name="materialButtonStyle">@style/Widget.CipherCellVPN.Button</item>
        <item name="switchStyle">@style/Widget.CipherCellVPN.Switch</item>
    </style>
    
    <!-- Card style -->
    <style name="Widget.CipherCellVPN.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">?attr/colorSurface</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="contentPadding">16dp</item>
        <item name="rippleColor">@color/primary</item>
    </style>
    
    <!-- Button style -->
    <style name="Widget.CipherCellVPN.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textSize">16sp</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">8dp</item>
        <item name="elevation">2dp</item>
    </style>
    
    <!-- Switch style -->
    <style name="Widget.CipherCellVPN.Switch" parent="Widget.MaterialComponents.CompoundButton.Switch">
        <item name="thumbTint">@color/primary</item>
        <item name="trackTint">@color/primary_variant</item>
    </style>
    
    <!-- Text Appearances -->
    <style name="TextAppearance.CipherCellVPN.Headline6" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:textSize">20sp</item>
    </style>
    
    <style name="TextAppearance.CipherCellVPN.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:textSize">16sp</item>
    </style>
</resources>