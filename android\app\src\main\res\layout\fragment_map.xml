<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.gms.maps.MapView
        android:id="@+id/map_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/status_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Status Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/status_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/status_disconnected"
                    android:textAppearance="?attr/textAppearanceBody1" />

                <TextView
                    android:id="@+id/imsi_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/imsi_not_generated"
                    android:textAppearance="?attr/textAppearanceBody2" />
            </LinearLayout>

            <!-- VPN Provider Selection -->
            <com.google.android.material.chip.ChipGroup
                android:id="@+id/vpn_provider_chips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:singleSelection="true"
                app:selectionRequired="true">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_wireguard"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="WireGuard"
                    style="@style/Widget.MaterialComponents.Chip.Choice" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_nordvpn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="NordVPN"
                    style="@style/Widget.MaterialComponents.Chip.Choice" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_mullvad"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mullvad"
                    style="@style/Widget.MaterialComponents.Chip.Choice" />

            </com.google.android.material.chip.ChipGroup>

            <!-- Controls -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/connect_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="@string/connect" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/rotate_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="@string/rotate"
                    app:backgroundTint="?attr/colorSecondary" />

            </LinearLayout>

            <!-- Auto Rotation Switch -->
            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/auto_rotate_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/auto_rotate" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>