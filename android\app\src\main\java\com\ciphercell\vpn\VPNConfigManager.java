package com.ciphercell.vpn;

import android.content.Context;
import android.content.SharedPreferences;

import org.json.JSONObject;
import org.json.JSONException;

import java.util.HashMap;
import java.util.Map;

public class VPNConfigManager {
    private static final String PREFS_NAME = "vpn_config_prefs";
    private static final String KEY_WIREGUARD_CONFIG = "wireguard_config";
    private static final String KEY_NORDVPN_CONFIG = "nordvpn_config";
    private static final String KEY_MULLVAD_CONFIG = "mullvad_config";
    
    private final Context context;
    private final SharedPreferences prefs;
    
    public VPNConfigManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    public void saveWireGuardConfig(String privateKey, String publicKey, String endpoint,
                                   String allowedIPs, String dns) {
        try {
            JSONObject config = new JSONObject();
            config.put("private_key", privateKey);
            config.put("public_key", publicKey);
            config.put("endpoint", endpoint);
            config.put("allowed_ips", allowedIPs);
            config.put("dns", dns);
            
            prefs.edit().putString(KEY_WIREGUARD_CONFIG, config.toString()).apply();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
    
    public void saveNordVPNConfig(String username, String password, String protocol) {
        try {
            JSONObject config = new JSONObject();
            config.put("username", username);
            config.put("password", password);
            config.put("protocol", protocol);
            
            prefs.edit().putString(KEY_NORDVPN_CONFIG, config.toString()).apply();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
    
    public void saveMullvadConfig(String accountNumber, String protocol) {
        try {
            JSONObject config = new JSONObject();
            config.put("account_number", accountNumber);
            config.put("protocol", protocol);
            
            prefs.edit().putString(KEY_MULLVAD_CONFIG, config.toString()).apply();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
    
    public Map<String, String> getWireGuardConfig() {
        Map<String, String> config = new HashMap<>();
        try {
            String configStr = prefs.getString(KEY_WIREGUARD_CONFIG, null);
            if (configStr != null) {
                JSONObject json = new JSONObject(configStr);
                config.put("private_key", json.getString("private_key"));
                config.put("public_key", json.getString("public_key"));
                config.put("endpoint", json.getString("endpoint"));
                config.put("allowed_ips", json.getString("allowed_ips"));
                config.put("dns", json.getString("dns"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return config;
    }
    
    public Map<String, String> getNordVPNConfig() {
        Map<String, String> config = new HashMap<>();
        try {
            String configStr = prefs.getString(KEY_NORDVPN_CONFIG, null);
            if (configStr != null) {
                JSONObject json = new JSONObject(configStr);
                config.put("username", json.getString("username"));
                config.put("password", json.getString("password"));
                config.put("protocol", json.getString("protocol"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return config;
    }
    
    public Map<String, String> getMullvadConfig() {
        Map<String, String> config = new HashMap<>();
        try {
            String configStr = prefs.getString(KEY_MULLVAD_CONFIG, null);
            if (configStr != null) {
                JSONObject json = new JSONObject(configStr);
                config.put("account_number", json.getString("account_number"));
                config.put("protocol", json.getString("protocol"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return config;
    }
    
    public void clearAllConfigs() {
        prefs.edit()
            .remove(KEY_WIREGUARD_CONFIG)
            .remove(KEY_NORDVPN_CONFIG)
            .remove(KEY_MULLVAD_CONFIG)
            .apply();
    }
    
    public boolean hasConfig(String provider) {
        String configKey;
        switch (provider) {
            case "WireGuard":
                configKey = KEY_WIREGUARD_CONFIG;
                break;
            case "NordVPN":
                configKey = KEY_NORDVPN_CONFIG;
                break;
            case "Mullvad":
                configKey = KEY_MULLVAD_CONFIG;
                break;
            default:
                return false;
        }
        return prefs.contains(configKey);
    }
    
    public void updateServerEndpoint(String provider, String region, String endpoint) {
        try {
            switch (provider) {
                case "WireGuard":
                    String configStr = prefs.getString(KEY_WIREGUARD_CONFIG, null);
                    if (configStr != null) {
                        JSONObject config = new JSONObject(configStr);
                        config.put("endpoint", endpoint);
                        prefs.edit().putString(KEY_WIREGUARD_CONFIG, config.toString()).apply();
                    }
                    break;
                    
                case "NordVPN":
                case "Mullvad":
                    // Implement server endpoint updates for other providers
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}