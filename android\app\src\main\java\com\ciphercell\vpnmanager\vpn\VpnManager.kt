package com.ciphercell.vpnmanager.vpn

import android.content.Context
import com.wireguard.android.backend.GoBackend
import com.wireguard.android.backend.Tunnel
import com.wireguard.config.Config
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VpnManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    private val _vpnState = MutableStateFlow<Tunnel.State>(Tunnel.State.DOWN)
    val vpnState = _vpnState.asStateFlow()

    private var backend: GoBackend? = null
    private var tunnel: WireGuardTunnel? = null

    init {
        backend = GoBackend(context)
    }

    fun connect(config: Config) {
        try {
            if (tunnel != null) {
                disconnect()
            }
            val newTunnel = WireGuardTunnel("wg_tunnel", config)
            backend?.setState(newTunnel, Tunnel.State.UP, config)
            tunnel = newTunnel
            _vpnState.value = Tunnel.State.UP
        } catch (e: Exception) {
            _vpnState.value = Tunnel.State.DOWN
        }
    }

    fun disconnect() {
        tunnel?.let {
            backend?.setState(it, Tunnel.State.DOWN, null)
            _vpnState.value = Tunnel.State.DOWN
            tunnel = null
        }
    }

    private class WireGuardTunnel(private val name: String, private val config: Config) : Tunnel {
        override fun getName(): String = name
        override fun onStateChange(newState: Tunnel.State) {
            // No-op
        }
    }
}
