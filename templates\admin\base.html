<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    {% if current_user.is_authenticated %}
    <div class="flex h-screen">
        <!-- Mobile Menu Button -->
        <button id="mobile-menu-button" class="lg:hidden fixed top-4 left-4 z-20 p-2 rounded-md bg-gray-800 text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-600">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 transform -translate-x-full lg:relative lg:translate-x-0 transition duration-200 ease-in-out w-64 bg-gray-800 text-white z-10 shadow-lg">
            <div class="p-4">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <a href="/admin/dashboard" class="block py-2.5 px-4 hover:bg-gray-700 {% if request.endpoint == 'admin_dashboard' %}bg-gray-700{% endif %}">
                    <i class="fas fa-home mr-2"></i> Dashboard
                </a>
                <a href="/admin/esim" class="block py-2.5 px-4 hover:bg-gray-700 {% if request.endpoint == 'admin_esim' %}bg-gray-700{% endif %}">
                    <i class="fas fa-sim-card mr-2"></i> eSIM Management
                </a>
                <a href="/admin/vpn" class="block py-2.5 px-4 hover:bg-gray-700 {% if request.endpoint == 'admin_vpn' %}bg-gray-700{% endif %}">
                    <i class="fas fa-shield-alt mr-2"></i> VPN Management
                </a>
                <a href="/admin/databundles" class="block py-2.5 px-4 hover:bg-gray-700 {% if request.endpoint == 'admin_databundles' %}bg-gray-700{% endif %}">
                    <i class="fas fa-box mr-2"></i> Data Bundles
                </a>
                <a href="/admin/vps" class="block py-2.5 px-4 hover:bg-gray-700 {% if request.endpoint == 'admin_vps' %}bg-gray-700{% endif %}">
                    <i class="fas fa-server mr-2"></i> VPS Management
                </a>
                {% if current_user.role == 'admin' %}
                <a href="/admin/users" class="block py-2.5 px-4 hover:bg-gray-700 {% if request.endpoint == 'admin_users' %}bg-gray-700{% endif %}">
                    <i class="fas fa-users mr-2"></i> User Management
                </a>
                {% endif %}
                <a href="{{ url_for('admin_logout') }}" class="block py-2.5 px-4 hover:bg-gray-700">
                    <i class="fas fa-sign-out-alt mr-2"></i> Logout
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow">
                <div class="p-4 flex justify-between items-center lg:ml-0 ml-16">
                    <div class="flex items-center">
                        <span class="text-xl font-semibold">{% block header %}{% endblock %}</span>
                    </div>
                    <div class="flex items-center">
                        <span class="mr-4">Welcome, {{ current_user.username }}</span>
                    </div>
                </div>
            </header>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="p-4 {{ 'bg-green-100 text-green-700' if category == 'success' else 'bg-red-100 text-red-700' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Main Content Area -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    {% else %}
        {% block auth_content %}{% endblock %}
    {% endif %}

    {% block scripts %}{% endblock %}
    <script>
        // Mobile menu toggle and overlay
        const overlay = document.createElement('div');
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-0 hidden lg:hidden';
        document.body.appendChild(overlay);

        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const sidebar = document.getElementById('sidebar');

        if (mobileMenuButton && sidebar) {
            const toggleMenu = () => {
                sidebar.classList.toggle('-translate-x-full');
                overlay.classList.toggle('hidden');
                document.body.classList.toggle('overflow-hidden');
            };

            mobileMenuButton.addEventListener('click', toggleMenu);

            // Close sidebar when clicking overlay
            overlay.addEventListener('click', toggleMenu);

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', (e) => {
                if (window.innerWidth < 1024 && // lg breakpoint
                    !sidebar.contains(e.target) && 
                    !mobileMenuButton.contains(e.target) && 
                    !sidebar.classList.contains('-translate-x-full')) {
                    toggleMenu();
                }
            });

            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024) {
                    sidebar.classList.remove('-translate-x-full');
                    overlay.classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                }
            });
        }
    </script>
</body>
</html>