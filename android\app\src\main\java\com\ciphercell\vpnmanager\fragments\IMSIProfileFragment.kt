package com.ciphercell.vpnmanager.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.ciphercell.vpnmanager.R
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class IMSIProfileFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Re-using the layout from ESimProfileFragment
        return inflater.inflate(R.layout.fragment_esim_profile, container, false)
    }
}
