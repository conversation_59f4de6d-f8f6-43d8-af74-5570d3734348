<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Complete CiphercellVPN backend with eSIM provisioning, VPN management, and prepaid services">
    <meta name="keywords" content="CiphercellVPN, eSIM, VPN, WireGuard, prepaid, mobile network">
    <meta name="author" content="CipherCell VPN">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CipherCell">
    <title>CiphercellVPN - Dashboard</title>
    <link rel="manifest" href="/static/manifest.json">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="apple-touch-icon" href="/static/icon-192x192.png">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            font-size: 2rem;
            margin-right: 15px;
            color: #667eea;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .result-card.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .qr-code {
            text-align: center;
            margin: 20px 0;
        }

        .qr-code img {
            max-width: 200px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .info-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }

        .info-value {
            font-weight: 600;
            color: #333;
            word-break: break-all;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active {
            background-color: #28a745;
        }

        .status-inactive {
            background-color: #dc3545;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .config-text {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            font-size: 0.9rem;
        }

        .copy-btn:hover {
            background: #218838;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .dashboard {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> CipherCell VPN</h1>
            <p>Secure CiphercellVPN Backend with eSIM Provisioning & VPN Services</p>
        </div>

        <div class="dashboard">
            <!-- eSIM Provisioning Card -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-sim-card card-icon"></i>
                    <h2 class="card-title">eSIM Provisioning</h2>
                </div>
                
                <form id="provisionForm">
                    <div class="form-group">
                        <label for="planSelect">Subscription Plan</label>
                        <select id="planSelect" class="form-control">
                            <option value="basic">Basic (1GB/month)</option>
                            <option value="premium">Premium (5GB/month)</option>
                            <option value="unlimited">Unlimited</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn">
                        <i class="fas fa-plus"></i> Provision New eSIM
                    </button>
                </form>

                <div class="loading" id="provisionLoading">
                    <div class="spinner"></div>
                    <p>Provisioning eSIM...</p>
                </div>

                <div class="alert alert-success" id="provisionSuccess"></div>
                <div class="alert alert-error" id="provisionError"></div>

                <div class="result-card" id="provisionResult">
                    <h3>eSIM Provisioned Successfully!</h3>
                    <div class="info-grid" id="simInfo"></div>
                    <div class="qr-code" id="simQR"></div>
                </div>
            </div>

            <!-- Subscription Status Card -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-user-check card-icon"></i>
                    <h2 class="card-title">Check Status</h2>
                </div>
                
                <form id="statusForm">
                    <div class="form-group">
                        <label for="imsiInput">IMSI Number</label>
                        <input type="text" id="imsiInput" class="form-control" 
                               placeholder="Enter IMSI (e.g., 001011234567890)" 
                               pattern="[0-9]{15}" maxlength="15">
                    </div>
                    
                    <button type="submit" class="btn">
                        <i class="fas fa-search"></i> Check Status
                    </button>
                </form>

                <div class="loading" id="statusLoading">
                    <div class="spinner"></div>
                    <p>Checking status...</p>
                </div>

                <div class="alert alert-error" id="statusError"></div>

                <div class="result-card" id="statusResult">
                    <h3>Subscription Status</h3>
                    <div class="info-grid" id="statusInfo"></div>
                </div>
            </div>

            <!-- VPN Configuration Card -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-vpn-card card-icon"></i>
                    <h2 class="card-title">VPN Configuration</h2>
                </div>
                
                <form id="vpnForm">
                    <div class="form-group">
                        <label for="userIdInput">User ID</label>
                        <input type="number" id="userIdInput" class="form-control" 
                               placeholder="Enter User ID" min="1">
                    </div>
                    
                    <button type="submit" class="btn">
                        <i class="fas fa-download"></i> Get VPN Config
                    </button>
                </form>

                <div class="loading" id="vpnLoading">
                    <div class="spinner"></div>
                    <p>Generating VPN config...</p>
                </div>

                <div class="alert alert-error" id="vpnError"></div>

                <div class="result-card" id="vpnResult">
                    <h3>VPN Configuration</h3>
                    <div class="config-text" id="vpnConfig"></div>
                    <button class="copy-btn" onclick="copyVPNConfig()">
                        <i class="fas fa-copy"></i> Copy Configuration
                    </button>
                    <div class="qr-code" id="vpnQR"></div>
                </div>
            </div>

            <!-- System Status Card -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-server card-icon"></i>
                    <h2 class="card-title">System Status</h2>
                </div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">API Status</div>
                        <div class="info-value">
                            <span class="status-indicator status-active"></span>
                            Online
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">VPN Server</div>
                        <div class="info-value">
                            <span class="status-indicator status-active"></span>
                            Running
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Server IP</div>
                        <div class="info-value">*************</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Domain</div>
                        <div class="info-value">ciphercellvpn.com</div>
                    </div>
                </div>
                
                <button class="btn" onclick="checkSystemHealth()">
                    <i class="fas fa-heartbeat"></i> Check System Health
                </button>
            </div>
        </div>
    </div>

    <script>
        // API Base URL
        const API_BASE = '/api';

        // Utility functions
        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }

        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        function showAlert(elementId, message) {
            const alert = document.getElementById(elementId);
            alert.textContent = message;
            alert.style.display = 'block';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        function hideAlerts(prefix) {
            document.getElementById(prefix + 'Success').style.display = 'none';
            document.getElementById(prefix + 'Error').style.display = 'none';
        }

        // eSIM Provisioning
        document.getElementById('provisionForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const plan = document.getElementById('planSelect').value;
            hideAlerts('provision');
            showLoading('provisionLoading');
            
            try {
                const response = await fetch(`${API_BASE}/provision`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ plan })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayProvisionResult(data);
                    showAlert('provisionSuccess', 'eSIM provisioned successfully!');
                } else {
                    showAlert('provisionError', data.error || 'Provisioning failed');
                }
            } catch (error) {
                showAlert('provisionError', 'Network error: ' + error.message);
            } finally {
                hideLoading('provisionLoading');
            }
        });

        function displayProvisionResult(data) {
            const simInfo = document.getElementById('simInfo');
            simInfo.innerHTML = `
                <div class="info-item">
                    <div class="info-label">User ID</div>
                    <div class="info-value">${data.user_id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">IMSI</div>
                    <div class="info-value">${data.imsi}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">MSISDN</div>
                    <div class="info-value">${data.msisdn}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Plan</div>
                    <div class="info-value">${data.subscription.plan}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Expires</div>
                    <div class="info-value">${new Date(data.subscription.expires).toLocaleDateString()}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">VPN IP</div>
                    <div class="info-value">${data.vpn.ip_address}</div>
                </div>
            `;
            
            const simQR = document.getElementById('simQR');
            simQR.innerHTML = `
                <h4>eSIM QR Code</h4>
                <img src="data:image/png;base64,${data.qr_codes.sim}" alt="eSIM QR Code">
                <h4>VPN QR Code</h4>
                <img src="data:image/png;base64,${data.qr_codes.vpn}" alt="VPN QR Code">
            `;
            
            document.getElementById('provisionResult').classList.add('show');
        }

        // Status Check
        document.getElementById('statusForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const imsi = document.getElementById('imsiInput').value;
            if (!imsi || imsi.length !== 15) {
                showAlert('statusError', 'Please enter a valid 15-digit IMSI');
                return;
            }
            
            hideAlerts('status');
            showLoading('statusLoading');
            
            try {
                const response = await fetch(`${API_BASE}/status/${imsi}`);
                const data = await response.json();
                
                if (data.success) {
                    displayStatusResult(data);
                } else {
                    showAlert('statusError', data.error || 'Status check failed');
                }
            } catch (error) {
                showAlert('statusError', 'Network error: ' + error.message);
            } finally {
                hideLoading('statusLoading');
            }
        });

        function displayStatusResult(data) {
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.innerHTML = `
                <div class="info-item">
                    <div class="info-label">User ID</div>
                    <div class="info-value">${data.user_id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">IMSI</div>
                    <div class="info-value">${data.imsi}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">MSISDN</div>
                    <div class="info-value">${data.msisdn}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                        <span class="status-indicator ${data.status === 'active' ? 'status-active' : 'status-inactive'}"></span>
                        ${data.status}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">Plan</div>
                    <div class="info-value">${data.subscription.plan}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Data Usage</div>
                    <div class="info-value">${data.subscription.data_used_mb}MB / ${data.subscription.data_limit_mb}MB</div>
                </div>
            `;
            
            document.getElementById('statusResult').classList.add('show');
        }

        // VPN Configuration
        document.getElementById('vpnForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const userId = document.getElementById('userIdInput').value;
            if (!userId) {
                showAlert('vpnError', 'Please enter a valid User ID');
                return;
            }
            
            hideAlerts('vpn');
            showLoading('vpnLoading');
            
            try {
                const response = await fetch(`${API_BASE}/vpn/${userId}`);
                const data = await response.json();
                
                if (data.success) {
                    displayVPNResult(data);
                    
                    // Get QR code
                    const qrResponse = await fetch(`${API_BASE}/qr/${userId}`);
                    const qrData = await qrResponse.json();
                    if (qrData.success) {
                        document.getElementById('vpnQR').innerHTML = `
                            <h4>VPN QR Code</h4>
                            <img src="data:image/png;base64,${qrData.qr_code}" alt="VPN QR Code">
                        `;
                    }
                } else {
                    showAlert('vpnError', data.error || 'VPN config retrieval failed');
                }
            } catch (error) {
                showAlert('vpnError', 'Network error: ' + error.message);
            } finally {
                hideLoading('vpnLoading');
            }
        });

        function displayVPNResult(data) {
            document.getElementById('vpnConfig').textContent = data.config;
            document.getElementById('vpnResult').classList.add('show');
        }

        function copyVPNConfig() {
            const config = document.getElementById('vpnConfig').textContent;
            navigator.clipboard.writeText(config).then(() => {
                alert('VPN configuration copied to clipboard!');
            }).catch(() => {
                alert('Failed to copy configuration');
            });
        }

        // System Health Check
        async function checkSystemHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    alert('System is healthy and running normally!');
                } else {
                    alert('System health check failed');
                }
            } catch (error) {
                alert('Failed to check system health: ' + error.message);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            console.log('CipherCell VPN Dashboard loaded');
        });
    </script>
    <script src="/static/js/app.js"></script>
</body>
</html>