package com.ciphercell.vpnmanager;

import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.ui.NavigationUI;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public class MainActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set up immersive status bar
        setupImmersiveMode();

        setContentView(R.layout.activity_main);

        // Setup navigation
        setupNavigation();
    }

    private void setupImmersiveMode() {
        // Make status bar transparent
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        );

        // Set status bar color
        getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.transparent));

        // Make status bar icons light (for dark background)
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
    }

    private void setupNavigation() {
        try {
            NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment);
            BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);

            if (bottomNav != null) {
                NavigationUI.setupWithNavController(bottomNav, navController);
            }
        } catch (Exception e) {
            // Navigation setup failed, app will still work but without navigation
            e.printStackTrace();
        }
    }
}