{% extends "base.html" %}

{% block title %}Dashboard - SM-DP+ Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt"></i> Dashboard
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
                <i class="fas fa-sync-alt"></i> Vernieuwen
            </button>
        </div>
    </div>
</div>

<!-- Statistieken Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Totaal Profielen</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_profiles }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-id-card fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Actieve Profielen</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.active_profiles }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Geactiveerd</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.activated_profiles }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-download fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Server Status</div>
                        <div class="h6 mb-0 font-weight-bold">Online</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Grafieken Row -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area"></i> Activaties Overzicht
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="activationsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie"></i> Status Verdeling
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recente Activiteiten -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history"></i> Recente Activiteiten
                </h6>
            </div>
            <div class="card-body">
                {% if stats.recent_activities %}
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Tijd</th>
                                <th>Actie</th>
                                <th>Resource</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for activity in stats.recent_activities %}
                            <tr>
                                <td>{{ activity[0]|datetime }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ activity[1] }}</span>
                                </td>
                                <td>{{ activity[2] }}</td>
                                <td>{{ activity[3] or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Nog geen activiteiten geregistreerd.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt"></i> Snelle Acties
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('generate_profile') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-plus-circle"></i><br>
                            Nieuw eSIM Profiel
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('profiles') }}" class="btn btn-info btn-block">
                            <i class="fas fa-list"></i><br>
                            Alle Profielen
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('audit_log') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-history"></i><br>
                            Audit Log
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-success btn-block" onclick="downloadBackup()">
                            <i class="fas fa-download"></i><br>
                            Backup Downloaden
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Refresh statistieken
function refreshStats() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            console.log('Stats updated:', data);
            // Update charts met nieuwe data
            updateCharts(data);
        })
        .catch(error => {
            console.error('Error refreshing stats:', error);
        });
}

// Charts initialiseren
let activationsChart, statusChart;

document.addEventListener('DOMContentLoaded', function() {
    // Activations Chart
    const activationsCtx = document.getElementById('activationsChart').getContext('2d');
    activationsChart = new Chart(activationsCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Activaties',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Activaties per Dag (Laatste 30 dagen)'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Actief', 'Inactief', 'Geactiveerd'],
            datasets: [{
                data: [{{ stats.active_profiles }}, 0, {{ stats.activated_profiles }}],
                backgroundColor: [
                    '#36b9cc',
                    '#f6c23e',
                    '#1cc88a'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Load initial stats
    refreshStats();
});

function updateCharts(data) {
    // Update activations chart
    if (data.activations_per_day) {
        const dates = Object.keys(data.activations_per_day);
        const counts = Object.values(data.activations_per_day);
        
        activationsChart.data.labels = dates;
        activationsChart.data.datasets[0].data = counts;
        activationsChart.update();
    }
    
    // Update status chart
    if (data.profiles_by_status) {
        const active = data.profiles_by_status.active || 0;
        const inactive = data.profiles_by_status.inactive || 0;
        
        statusChart.data.datasets[0].data = [active, inactive, {{ stats.activated_profiles }}];
        statusChart.update();
    }
}

function downloadBackup() {
    alert('Backup functionaliteit wordt binnenkort toegevoegd.');
}

// Auto-refresh elke 30 seconden
setInterval(refreshStats, 30000);
</script>
{% endblock %}