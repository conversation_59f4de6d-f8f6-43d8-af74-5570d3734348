package com.ciphercell.vpn;

import android.app.Activity;
import android.content.Intent;
import android.net.VpnService;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

public class MainActivity extends AppCompatActivity {
    private static final int AUTO_ROTATE_INTERVAL = 30 * 60 * 1000; // 30 minutes
    
    private Handler autoRotateHandler;
    private boolean isAutoRotating = false;
    
    private final ActivityResultLauncher<Intent> vpnPermissionLauncher =
        registerForActivityResult(new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == Activity.RESULT_OK) {
                    startVpnService(pendingVpnProvider, pendingVpnRegion);
                } else {
                    Toast.makeText(this, R.string.error_vpn_permission,
                        Toast.LENGTH_SHORT).show();
                }
            });
    
    private String pendingVpnProvider;
    private String pendingVpnRegion;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        autoRotateHandler = new Handler(Looper.getMainLooper());
        
        if (savedInstanceState == null) {
            loadMapFragment();
        }
    }
    
    private void loadMapFragment() {
        Fragment mapFragment = new MapFragment();
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.fragment_container, mapFragment);
        transaction.commit();
    }
    
    public void startVpnConnection(String provider, String region) {
        pendingVpnProvider = provider;
        pendingVpnRegion = region;
        
        Intent vpnIntent = VpnService.prepare(this);
        if (vpnIntent != null) {
            vpnPermissionLauncher.launch(vpnIntent);
        } else {
            startVpnService(provider, region);
        }
    }
    
    private void startVpnService(String provider, String region) {
        Intent serviceIntent = new Intent(this, VPNService.class);
        serviceIntent.setAction("CONNECT");
        serviceIntent.putExtra("provider", provider);
        serviceIntent.putExtra("region", region);
        startService(serviceIntent);
        
        // Save last connection for auto-connect on boot
        BootReceiver.saveLastConnection(this, provider, region);
    }
    
    public void disconnectVpn() {
        Intent serviceIntent = new Intent(this, VPNService.class);
        serviceIntent.setAction("DISCONNECT");
        startService(serviceIntent);
        stopAutoRotation();
    }
    
    public void rotateConnection(String provider) {
        IMSIManager imsiManager = new IMSIManager(this);
        String newImsi = imsiManager.generateNewIMSI(pendingVpnRegion);
        
        // Reconnect VPN with new IMSI
        disconnectVpn();
        startVpnConnection(provider, pendingVpnRegion);
        
        Toast.makeText(this, getString(R.string.msg_imsi_generated, newImsi),
            Toast.LENGTH_SHORT).show();
    }
    
    public void setAutoRotation(boolean enabled) {
        isAutoRotating = enabled;
        if (enabled) {
            startAutoRotation();
        } else {
            stopAutoRotation();
        }
    }
    
    private void startAutoRotation() {
        autoRotateHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isAutoRotating) {
                    rotateConnection(pendingVpnProvider);
                    autoRotateHandler.postDelayed(this, AUTO_ROTATE_INTERVAL);
                }
            }
        }, AUTO_ROTATE_INTERVAL);
        
        Toast.makeText(this,
            getString(R.string.msg_auto_rotation_enabled, AUTO_ROTATE_INTERVAL / 60000),
            Toast.LENGTH_SHORT).show();
    }
    
    private void stopAutoRotation() {
        isAutoRotating = false;
        autoRotateHandler.removeCallbacksAndMessages(null);
    }
    
    @Override
    protected void onDestroy() {
        stopAutoRotation();
        super.onDestroy();
    }
}