<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <!-- Fragment Container -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:defaultNavHost="true"
        app:navGraph="@navigation/nav_graph" />

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorSurface"
        app:itemIconTint="@drawable/bottom_nav_selector"
        app:itemTextColor="@drawable/bottom_nav_selector"
        app:labelVisibilityMode="labeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:menu="@menu/bottom_nav_menu" />

</androidx.constraintlayout.widget.ConstraintLayout>
                        android:text="IMSI: Not assigned" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- VPN Selection Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="VPN Selection"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/vpn_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp">

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/wireguard_radio"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="WireGuard"
                            android:checked="true" />

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/nordvpn_radio"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="NordVPN" />

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/mullvad_radio"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mullvad" />

                    </RadioGroup>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Region & IMSI Settings Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Region & IMSI Settings"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:id="@+id/region_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/us_radio"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="US"
                            android:checked="true" />

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/eu_radio"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="EU" />

                        <com.google.android.material.radiobutton.MaterialRadioButton
                            android:id="@+id/asia_radio"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Asia" />

                    </RadioGroup>

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/auto_rotate_switch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Auto-rotate IMSI" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Control Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/connect_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Connect"
                    app:cornerRadius="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/disconnect_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Disconnect"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:cornerRadius="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/rotate_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Rotate IMSI"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>