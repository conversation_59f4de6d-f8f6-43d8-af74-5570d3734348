{% extends "admin/base.html" %}

{% block content %}
<div class="p-4">
    <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold">VPN Configuration Management</h1>
        <button onclick="openAddModal()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Add New Configuration
        </button>
    </div>

    <!-- Search and Filter -->
    <div class="mb-4 flex gap-4">
        <input type="text" id="searchInput" placeholder="Search configurations..." class="p-2 border rounded flex-grow">
        <select id="typeFilter" class="p-2 border rounded">
            <option value="">All Types</option>
            <option value="wireguard">WireGuard</option>
            <option value="openvpn">OpenVPN</option>
            <option value="ikev2">IKEv2</option>
        </select>
    </div>

    <!-- VPN Configurations Table -->
    <div class="bg-white shadow-md rounded my-6 overflow-x-auto">
        <table class="min-w-full table-auto">
            <thead>
                <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left">Name</th>
                    <th class="py-3 px-6 text-left">Type</th>
                    <th class="py-3 px-6 text-left">Server</th>
                    <th class="py-3 px-6 text-left">Port</th>
                    <th class="py-3 px-6 text-left">Created At</th>
                    <th class="py-3 px-6 text-center">Actions</th>
                </tr>
            </thead>
            <tbody id="vpnConfigTable" class="text-gray-600 text-sm">
                <!-- Table content will be populated by JavaScript -->
            </tbody>
        </table>
    </div>

    <!-- Add/Edit Modal -->
    <div id="configModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4" id="modalTitle">Add New Configuration</h3>
                <form id="configForm" class="space-y-4">
                    <input type="hidden" id="configId">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" id="configName" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Type</label>
                        <select id="configType" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="wireguard">WireGuard</option>
                            <option value="openvpn">OpenVPN</option>
                            <option value="ikev2">IKEv2</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Server</label>
                        <input type="text" id="configServer" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Port</label>
                        <input type="number" id="configPort" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Configuration</label>
                        <textarea id="configContent" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" rows="4"></textarea>
                    </div>
                    <div class="flex justify-end gap-3">
                        <button type="button" onclick="closeModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Cancel</button>
                        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let configs = [];

// Fetch and display VPN configurations
async function fetchConfigs() {
    try {
        const response = await fetch('/api/vpn/configs');
        configs = await response.json();
        displayConfigs();
    } catch (error) {
        console.error('Error fetching configs:', error);
    }
}

// Display configurations in table
function displayConfigs() {
    const tbody = document.getElementById('vpnConfigTable');
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;

    const filteredConfigs = configs.filter(config => {
        const matchesSearch = config.name.toLowerCase().includes(searchTerm) ||
                             config.server.toLowerCase().includes(searchTerm);
        const matchesType = !typeFilter || config.type === typeFilter;
        return matchesSearch && matchesType;
    });

    tbody.innerHTML = filteredConfigs.map(config => `
        <tr class="border-b border-gray-200 hover:bg-gray-100">
            <td class="py-3 px-6 text-left">${config.name}</td>
            <td class="py-3 px-6 text-left">${config.type}</td>
            <td class="py-3 px-6 text-left">${config.server}</td>
            <td class="py-3 px-6 text-left">${config.port}</td>
            <td class="py-3 px-6 text-left">${new Date(config.created_at).toLocaleString()}</td>
            <td class="py-3 px-6 text-center">
                <button onclick="editConfig(${config.id})" class="text-blue-600 hover:text-blue-900 mr-3">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteConfig(${config.id})" class="text-red-600 hover:text-red-900">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// Modal functions
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'Add New Configuration';
    document.getElementById('configForm').reset();
    document.getElementById('configId').value = '';
    document.getElementById('configModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('configModal').classList.add('hidden');
}

// Edit configuration
async function editConfig(id) {
    const config = configs.find(c => c.id === id);
    if (config) {
        document.getElementById('modalTitle').textContent = 'Edit Configuration';
        document.getElementById('configId').value = config.id;
        document.getElementById('configName').value = config.name;
        document.getElementById('configType').value = config.type;
        document.getElementById('configServer').value = config.server;
        document.getElementById('configPort').value = config.port;
        document.getElementById('configContent').value = config.config;
        document.getElementById('configModal').classList.remove('hidden');
    }
}

// Delete configuration
async function deleteConfig(id) {
    if (confirm('Are you sure you want to delete this configuration?')) {
        try {
            const response = await fetch(`/api/vpn/config/${id}`, {
                method: 'DELETE'
            });
            if (response.ok) {
                await fetchConfigs();
            } else {
                alert('Failed to delete configuration');
            }
        } catch (error) {
            console.error('Error deleting config:', error);
        }
    }
}

// Form submission
document.getElementById('configForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const configId = document.getElementById('configId').value;
    const configData = {
        name: document.getElementById('configName').value,
        type: document.getElementById('configType').value,
        server: document.getElementById('configServer').value,
        port: parseInt(document.getElementById('configPort').value),
        config: document.getElementById('configContent').value
    };

    try {
        const url = configId ? `/api/vpn/config/${configId}` : '/api/vpn/config';
        const method = configId ? 'PUT' : 'POST';
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });

        if (response.ok) {
            closeModal();
            await fetchConfigs();
        } else {
            alert('Failed to save configuration');
        }
    } catch (error) {
        console.error('Error saving config:', error);
    }
});

// Search and filter event listeners
document.getElementById('searchInput').addEventListener('input', displayConfigs);
document.getElementById('typeFilter').addEventListener('change', displayConfigs);

// Initial load
fetchConfigs();
</script>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.4.4/build/qrcode.min.js"></script>
<script>
function showAddConfigModal() {
    document.getElementById('modalTitle').textContent = 'Add New Configuration';
    document.getElementById('configForm').reset();
    document.getElementById('configId').value = '';
    document.getElementById('configModal').classList.remove('hidden');
}

function showEditModal(configId) {
    document.getElementById('modalTitle').textContent = 'Edit Configuration';
    // Fetch and populate configuration data
    document.getElementById('configId').value = configId;
    document.getElementById('configModal').classList.remove('hidden');
}

function hideConfigModal() {
    document.getElementById('configModal').classList.add('hidden');
}

function showBatchImportModal() {
    document.getElementById('batchImportModal').classList.remove('hidden');
}

function hideBatchImportModal() {
    document.getElementById('batchImportModal').classList.add('hidden');
}

function showQRCode(configId) {
    // Generate and display QR code
    const qrcodeDiv = document.getElementById('qrcode');
    qrcodeDiv.innerHTML = '';
    // Example: QRCode.toCanvas(qrcodeDiv, configData);
    document.getElementById('qrModal').classList.remove('hidden');
}

function hideQRModal() {
    document.getElementById('qrModal').classList.add('hidden');
}

function downloadConfig(configId) {
    // Implement configuration download logic
}

function confirmDelete(configId) {
    if (confirm('Are you sure you want to delete this configuration?')) {
        // Implement delete logic
    }
}

document.getElementById('configForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Implement form submission logic
});

document.getElementById('batchImportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // Implement batch import logic
});

document.getElementById('search').addEventListener('input', function(e) {
    // Implement search logic
});

document.getElementById('vpnTypeFilter').addEventListener('change', function(e) {
    // Implement type filter logic
});
</script>
{% endblock %}
{% endblock %}