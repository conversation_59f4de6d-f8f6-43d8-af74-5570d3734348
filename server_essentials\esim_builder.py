#!/usr/bin/env python3
"""
eSIM Profile Builder with VPN Integration and IMSI Generation
Builds encrypted eSIM profiles with embedded VPN configurations and custom IMSI numbers
"""

import json
import os
import random
import string
import hashlib
import base64
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import secrets

class IMSIGenerator:
    """Generate valid IMSI numbers for eSIM profiles"""
    
    def __init__(self):
        # Mobile Country Codes (MCC) - using test/private ranges
        self.test_mccs = {
            '001': 'Test Network',
            '999': 'Private Network',
            '901': 'International Mobile Satellite',
            '902': 'International Networks'
        }
        
        # Mobile Network Codes (MNC) - 2 or 3 digits
        self.test_mncs = ['01', '02', '03', '99', '001', '002', '999']
    
    def generate_imsi(self, mcc=None, mnc=None, custom_prefix=None):
        """Generate a valid IMSI number"""
        if custom_prefix:
            # Use custom prefix if provided
            mcc_mnc = custom_prefix[:6].ljust(6, '0')
        else:
            # Use test MCC/MNC
            mcc = mcc or random.choice(list(self.test_mccs.keys()))
            mnc = mnc or random.choice(self.test_mncs)
            mcc_mnc = mcc + mnc.zfill(3 if len(mnc) == 3 else 2)
        
        # Generate MSIN (Mobile Subscriber Identification Number)
        # IMSI is 15 digits total, so MSIN is 15 - len(MCC+MNC)
        msin_length = 15 - len(mcc_mnc)
        msin = ''.join([str(random.randint(0, 9)) for _ in range(msin_length)])
        
        imsi = mcc_mnc + msin
        return imsi[:15]  # Ensure exactly 15 digits
    
    def generate_multiple_imsi(self, count=5, mcc=None, mnc=None):
        """Generate multiple IMSI numbers"""
        return [self.generate_imsi(mcc, mnc) for _ in range(count)]
    
    def validate_imsi(self, imsi):
        """Validate IMSI format"""
        if not imsi.isdigit():
            return False, "IMSI must contain only digits"
        if len(imsi) != 15:
            return False, "IMSI must be exactly 15 digits"
        return True, "Valid IMSI"

class VPNConfigGenerator:
    """Generate VPN configurations for eSIM profiles"""
    
    def __init__(self):
        self.vpn_servers = {
            'wireguard': {
                'protocol': 'WireGuard',
                'default_port': 51820,
                'encryption': 'ChaCha20Poly1305'
            },
            'openvpn': {
                'protocol': 'OpenVPN',
                'default_port': 1194,
                'encryption': 'AES-256-GCM'
            },
            'ikev2': {
                'protocol': 'IKEv2/IPSec',
                'default_port': 500,
                'encryption': 'AES-256'
            }
        }
    
    def generate_wireguard_config(self, server_ip, server_port=51820, dns_servers=None):
        """Generate WireGuard VPN configuration"""
        # Generate key pairs
        private_key = base64.b64encode(secrets.token_bytes(32)).decode('utf-8')
        public_key = base64.b64encode(secrets.token_bytes(32)).decode('utf-8')
        preshared_key = base64.b64encode(secrets.token_bytes(32)).decode('utf-8')
        
        # Generate client IP
        client_ip = f"10.{random.randint(0, 255)}.{random.randint(0, 255)}.{random.randint(2, 254)}/24"
        
        dns_servers = dns_servers or ['*******', '*******']
        
        config = {
            'type': 'wireguard',
            'interface': {
                'PrivateKey': private_key,
                'Address': client_ip,
                'DNS': dns_servers
            },
            'peer': {
                'PublicKey': public_key,
                'PresharedKey': preshared_key,
                'Endpoint': f"{server_ip}:{server_port}",
                'AllowedIPs': '0.0.0.0/0, ::/0',
                'PersistentKeepalive': 25
            }
        }
        
        return config
    
    def generate_openvpn_config(self, server_ip, server_port=1194):
        """Generate OpenVPN configuration"""
        config = {
            'type': 'openvpn',
            'client': True,
            'dev': 'tun',
            'proto': 'udp',
            'remote': f"{server_ip} {server_port}",
            'resolv-retry': 'infinite',
            'nobind': True,
            'persist-key': True,
            'persist-tun': True,
            'cipher': 'AES-256-GCM',
            'auth': 'SHA256',
            'key-direction': 1,
            'verb': 3
        }
        
        return config
    
    def generate_ikev2_config(self, server_ip, username, password):
        """Generate IKEv2/IPSec configuration"""
        config = {
            'type': 'ikev2',
            'server': server_ip,
            'username': username,
            'password': password,
            'encryption': 'AES-256',
            'integrity': 'SHA256',
            'dh_group': 14,
            'pfs': True,
            'dns_servers': ['*******', '*******']
        }
        
        return config

class eSIMProfileBuilder:
    """Build complete eSIM profiles with encryption"""
    
    def __init__(self):
        self.imsi_generator = IMSIGenerator()
        self.vpn_generator = VPNConfigGenerator()
        self.profiles_dir = "profiles"
        self.keys_dir = "secure_keys"  # Separate directory for key storage
        os.makedirs(self.profiles_dir, exist_ok=True)
        os.makedirs(self.keys_dir, exist_ok=True)
        
        # Security settings
        self.security_config = {
            'standard': {
                'level': 1,
                'encryption': 'AES-128',
                'key_size': 16,
                'iterations': 100000
            },
            'premium': {
                'level': 2,
                'encryption': 'AES-256',
                'key_size': 32,
                'iterations': 310000
            }
        }
    
    def generate_encryption_key(self, password=None, profile_type='standard'):
        """Generate encryption key based on profile type and security level"""
        security = self.security_config[profile_type]
        
        if password:
            # Derive key from password with security-level-specific parameters
            salt = secrets.token_bytes(security['key_size'])
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=security['key_size'],
                salt=salt,
                iterations=security['iterations']
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            
            # Generate key identifier
            key_id = hashlib.sha256(key + salt).hexdigest()[:16]
            
            # Store key metadata securely
            key_metadata = {
                'key_id': key_id,
                'salt': base64.b64encode(salt).decode(),
                'iterations': security['iterations'],
                'profile_type': profile_type,
                'created_at': datetime.now().isoformat()
            }
            
            return key, key_metadata
        else:
            # Generate random key for one-time use
            key = Fernet.generate_key()
            return key, None