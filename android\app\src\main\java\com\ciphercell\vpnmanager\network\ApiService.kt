package com.ciphercell.vpnmanager.network

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * Data class representing the WireGuard configuration received from the API.
 */
data class VpnConfigResponse(
    val config: String
)

/**
 * Retrofit service interface for the CipherCell VPN API.
 */
/**
 * Data class for the login request body.
 */
data class LoginRequest(
    val email: String,
    val pass: String // Using 'pass' as often 'password' is a reserved word or has special handling
)

/**
 * Data class for the login response.
 */
data class LoginResponse(
    val token: String
)

/**
 * Data class for the eSIM activation request.
 */
data class EsimActivationRequest(
    val activationCode: String
)

/**
 * Data class for the eSIM activation response.
 */
data class EsimActivationResponse(
    val success: Boolean,
    val message: String
)

/**
 * Retrofit API service interface for interacting with the backend.
 */
interface ApiService {

    /**
     * Fetches the WireGuard configuration from the backend.
     * This is a suspend function to be called from a coroutine.
     */
    @GET("api/v1/vpn/config")
    suspend fun getVpnConfig(@Header("Authorization") authToken: String): Response<VpnConfigResponse>

    /**
     * Authenticates the user and returns an auth token.
     *
     * @param loginRequest The user's credentials.
     * @return A [LoginResponse] containing the auth token.
     */
    @POST("api/auth/login")
    suspend fun loginUser(@Body loginRequest: LoginRequest): LoginResponse

    /**
     * Submits an eSIM activation request.
     *
     * @param authToken The authorization token for the user.
     * @param activationRequest The eSIM activation details.
     * @return An [EsimActivationResponse] indicating the result.
     */
    @POST("api/esim/activate")
    suspend fun activateEsim(
        @Header("Authorization") authToken: String,
        @Body activationRequest: EsimActivationRequest
    ): EsimActivationResponse
}
