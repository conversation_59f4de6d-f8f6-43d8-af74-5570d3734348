# PowerShell Deployment Script for CiphercellVPN Backend
# Uses SSH Key Authentication (Windows Compatible)
# VPS: ciphercellvpn.com (*************)

param(
    [string]$SSHKeyPath = "~\.ssh\id_rsa",
    [string]$VPSHost = "*************",
    [string]$VPSUser = "root",
    [string]$Domain = "ciphercellvpn.com"
)

# Colors for output
$Green = "`e[32m"
$Red = "`e[31m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-Log {
    param([string]$Message)
    Write-Host "${Green}[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $Message${Reset}"
}

function Write-Error-Log {
    param([string]$Message)
    Write-Host "${Red}[ERROR] $Message${Reset}"
    exit 1
}

function Write-Warning-Log {
    param([string]$Message)
    Write-Host "${Yellow}[WARNING] $Message${Reset}"
}

# Check if SSH key exists
if (-not (Test-Path $SSHKeyPath)) {
    Write-Error-Log "SSH key not found at $SSHKeyPath. Please ensure your SSH key is properly configured."
}

Write-Log "🚀 Starting CiphercellVPN deployment..."
Write-Log "📡 Host: $VPSHost"
Write-Log "🌐 Domain: $Domain"
Write-Log "🔑 Using SSH Key: $SSHKeyPath"

# Test SSH connection
Write-Log "🔌 Testing VPS connection..."
try {
    $testResult = ssh -i $SSHKeyPath -o StrictHostKeyChecking=no $VPSUser@$VPSHost "echo 'Connection successful'"
    if ($testResult -eq "Connection successful") {
        Write-Log "✅ VPS connection established"
    } else {
        Write-Error-Log "❌ Failed to connect to VPS"
    }
} catch {
    Write-Error-Log "❌ SSH connection failed: $($_.Exception.Message)"
}

# Create deployment directories on VPS
Write-Log "📁 Creating deployment directories..."
ssh -i $SSHKeyPath $VPSUser@$VPSHost "mkdir -p /opt/ciphercellvpn/{data,static,templates,logs}"
ssh -i $SSHKeyPath $VPSUser@$VPSHost "mkdir -p /etc/nginx/sites-available"
ssh -i $SSHKeyPath $VPSUser@$VPSHost "mkdir -p /etc/systemd/system"

# Upload application files
Write-Log "📤 Uploading application files..."
scp -i $SSHKeyPath app.py $VPSUser@${VPSHost}:/opt/ciphercellvpn/
scp -i $SSHKeyPath install_mvno_backend.sh $VPSUser@${VPSHost}:/opt/ciphercellvpn/
scp -i $SSHKeyPath requirements.txt $VPSUser@${VPSHost}:/opt/ciphercellvpn/
scp -i $SSHKeyPath nginx_mvno.conf $VPSUser@${VPSHost}:/etc/nginx/sites-available/

# Upload static files
Write-Log "📤 Uploading static files..."
scp -i $SSHKeyPath -r static/* $VPSUser@${VPSHost}:/opt/ciphercellvpn/static/

# Upload templates
Write-Log "📤 Uploading templates..."
scp -i $SSHKeyPath -r templates/* $VPSUser@${VPSHost}:/opt/ciphercellvpn/templates/

# Make installation script executable and run it
Write-Log "🔧 Running installation script..."
ssh -i $SSHKeyPath $VPSUser@$VPSHost "cd /opt/ciphercellvpn && chmod +x install_mvno_backend.sh && ./install_mvno_backend.sh"

# Check service status
Write-Log "🔍 Checking service status..."
$serviceStatus = ssh -i $SSHKeyPath $VPSUser@$VPSHost "systemctl is-active ciphercellvpn"
if ($serviceStatus -eq "active") {
    Write-Log "✅ CiphercellVPN service is running"
} else {
    Write-Warning-Log "⚠️ Service status: $serviceStatus"
}

# Check if website is accessible
Write-Log "🌐 Testing website accessibility..."
try {
    $response = Invoke-WebRequest -Uri "http://$Domain" -TimeoutSec 10 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Log "✅ Website is accessible at http://$Domain"
    }
} catch {
    Write-Warning-Log "⚠️ Website not yet accessible (SSL certificates may still be generating)"
}

Write-Log "🎉 Deployment completed!"
Write-Log "📱 Dashboard: https://$Domain"
Write-Log "⚙️ Admin Panel: https://$Domain/admin"
Write-Log "📚 API Docs: https://$Domain/docs"

Write-Host ""
Write-Host "${Blue}Next Steps:${Reset}"
Write-Host "1. Wait 2-3 minutes for SSL certificates to generate"
Write-Host "2. Visit https://$Domain to access your CiphercellVPN dashboard"
Write-Host "3. Check logs: ssh -i `$SSHKeyPath `$VPSUser@`$VPSHost 'journalctl -u ciphercellvpn -f'"
Write-Host "4. Monitor service: ssh -i `$SSHKeyPath `$VPSUser@`$VPSHost 'systemctl status ciphercellvpn'"