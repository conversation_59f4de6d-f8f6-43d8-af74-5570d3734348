package com.ciphercell.vpn;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.Random;
import java.util.HashSet;
import java.util.Set;

public class IMSIManager {
    private static final String PREFS_NAME = "imsi_prefs";
    private static final String CURRENT_IMSI = "current_imsi";
    private static final String IMSI_HISTORY = "imsi_history";
    
    private final Context context;
    private final SharedPreferences prefs;
    private final Random random;
    
    // MCC (Mobile Country Code) mappings
    private static final String[] US_MCC = {"310", "311", "312", "313", "316"};
    private static final String[] EU_MCC = {"232", "238", "244", "260", "262"}; // AT, DK, FI, PL, DE
    private static final String[] ASIA_MCC = {"454", "455", "460", "466", "502"}; // HK, MO, CN, TW, MY
    
    public IMSIManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.random = new Random();
    }
    
    public String getCurrentIMSI() {
        return prefs.getString(CURRENT_IMSI, null);
    }
    
    public String generateNewIMSI(String region) {
        String mcc = selectMCC(region);
        String mnc = generateMNC();
        String msin = generateMSIN();
        
        String imsi = mcc + mnc + msin;
        saveIMSI(imsi);
        return imsi;
    }
    
    private String selectMCC(String region) {
        String[] mccArray;
        switch (region.toUpperCase()) {
            case "US":
                mccArray = US_MCC;
                break;
            case "EU":
                mccArray = EU_MCC;
                break;
            case "ASIA":
                mccArray = ASIA_MCC;
                break;
            default:
                mccArray = US_MCC; // Default to US
        }
        return mccArray[random.nextInt(mccArray.length)];
    }
    
    private String generateMNC() {
        // Generate a random 2-3 digit MNC
        int mnc = random.nextInt(100);
        return String.format("%02d", mnc);
    }
    
    private String generateMSIN() {
        // Generate a random 10-digit MSIN
        StringBuilder msin = new StringBuilder();
        for (int i = 0; i < 10; i++) {
            msin.append(random.nextInt(10));
        }
        return msin.toString();
    }
    
    private void saveIMSI(String imsi) {
        // Save current IMSI
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(CURRENT_IMSI, imsi);
        
        // Add to history
        Set<String> history = new HashSet<>(prefs.getStringSet(IMSI_HISTORY, new HashSet<>()));
        history.add(imsi);
        editor.putStringSet(IMSI_HISTORY, history);
        
        editor.apply();
    }
    
    public Set<String> getIMSIHistory() {
        return prefs.getStringSet(IMSI_HISTORY, new HashSet<>());
    }
    
    public void clearHistory() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(IMSI_HISTORY);
        editor.apply();
    }
    
    public boolean isValidIMSI(String imsi) {
        // Basic IMSI validation
        if (imsi == null || imsi.length() < 14 || imsi.length() > 15) {
            return false;
        }
        
        // Check if all characters are digits
        for (char c : imsi.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        
        // Check if MCC is valid
        String mcc = imsi.substring(0, 3);
        boolean validMCC = false;
        for (String[] mccArray : new String[][]{US_MCC, EU_MCC, ASIA_MCC}) {
            for (String validMcc : mccArray) {
                if (validMcc.equals(mcc)) {
                    validMCC = true;
                    break;
                }
            }
            if (validMCC) break;
        }
        
        return validMCC;
    }
}