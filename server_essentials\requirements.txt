# Flask en web framework dependencies
Flask==2.3.3
Flask-Limiter==3.5.0
Werkzeug==2.3.7
Jinja2==3.1.2
MarkupSafe==2.1.3
Itsdangerous==2.1.2
click==8.1.7

# Cryptografie en beveiliging
cryptography==41.0.7
bcrypt==4.0.1
PyJWT==2.8.0

# QR-code generatie
qrcode[pil]==7.4.2
Pillow==10.0.1

# HTTP requests en API calls
requests==2.31.0
urllib3==2.0.7

# Data verwerking
pandas==2.1.3
numpy==1.25.2

# Datum en tijd
python-dateutil==2.8.2
pytz==2023.3

# Logging en monitoring
loguru==0.7.2

# Development en testing
pytest==7.4.3
pytest-flask==1.3.0
flake8==6.1.0
black==23.10.1

# Production server
gunicorn==21.2.0

# Environment variables
python-dotenv==1.0.0

# JSON en data serialization
orjson==3.9.10

# Rate limiting en caching
Flask-Caching==2.1.0
redis==5.0.1

# CORS support
Flask-CORS==4.0.0

# Session management
Flask-Session==0.5.0

# Form handling
WTForms==3.1.0
Flask-WTF==1.2.1

# Email notifications (optioneel)
Flask-Mail==0.9.1

# System utilities
psutil==5.9.6

# Configuration management
PyYAML==6.0.1