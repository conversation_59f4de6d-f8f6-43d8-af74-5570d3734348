{"name": "CiphercellVPN - Dashboard", "short_name": "CipherCell", "description": "Complete CiphercellVPN backend with eSIM provisioning, VPN management, and prepaid services", "start_url": "/", "display": "standalone", "background_color": "#667eea", "theme_color": "#667eea", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["business", "productivity", "utilities"], "icons": [{"src": "/static/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "/static/screenshot-wide.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "CipherCell Dashboard"}, {"src": "/static/screenshot-narrow.png", "sizes": "720x1280", "type": "image/png", "form_factor": "narrow", "label": "CipherCell Mobile View"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "View main dashboard", "url": "/", "icons": [{"src": "/static/icon-96x96.png", "sizes": "96x96"}]}, {"name": "Admin Panel", "short_name": "Admin", "description": "Access admin panel", "url": "/admin", "icons": [{"src": "/static/icon-96x96.png", "sizes": "96x96"}]}, {"name": "Provision eSIM", "short_name": "Provision", "description": "Provision new eSIM", "url": "/?tab=provision", "icons": [{"src": "/static/icon-96x96.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}