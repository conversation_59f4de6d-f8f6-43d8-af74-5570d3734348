{% extends "base.html" %}

{% block title %}eSIM Profielen - SM-DP+ Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-id-card"></i> eSIM Profielen
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('generate_profile') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle"></i> Nieuw Profiel
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshProfiles()">
                <i class="fas fa-sync-alt"></i> Vernieuwen
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">Status Filter</label>
                <select class="form-select" id="statusFilter" onchange="filterProfiles()">
                    <option value="">Alle Statussen</option>
                    <option value="active">Actief</option>
                    <option value="inactive">Inactief</option>
                    <option value="activated">Geactiveerd</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="searchInput" class="form-label">Zoeken</label>
                <input type="text" class="form-control" id="searchInput" placeholder="Activatiecode, IMSI..." onkeyup="filterProfiles()">
            </div>
            <div class="col-md-3">
                <label for="dateFilter" class="form-label">Aangemaakt</label>
                <input type="date" class="form-control" id="dateFilter" onchange="filterProfiles()">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Wissen
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Profielen Tabel -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-table"></i> Alle eSIM Profielen
            <span class="badge bg-secondary ms-2" id="profileCount">{{ profiles|length }}</span>
        </h6>
    </div>
    <div class="card-body">
        {% if profiles %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="profilesTable">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>Activatiecode</th>
                        <th>IMSI</th>
                        <th>IMEI</th>
                        <th>Status</th>
                        <th>Aangemaakt</th>
                        <th>Geactiveerd</th>
                        <th>Verloopt</th>
                        <th>Acties</th>
                    </tr>
                </thead>
                <tbody>
                    {% for profile in profiles %}
                    <tr data-status="{{ profile[4] }}" data-created="{{ profile[5][:10] }}">
                        <td>{{ profile[0] }}</td>
                        <td>
                            <code class="text-primary">{{ profile[1] }}</code>
                            <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('{{ profile[1] }}')" title="Kopiëren">
                                <i class="fas fa-copy"></i>
                            </button>
                        </td>
                        <td>
                            <small class="text-muted">{{ profile[2] }}</small>
                        </td>
                        <td>
                            <small class="text-muted">{{ profile[3] }}</small>
                        </td>
                        <td>
                            {% if profile[4] == 'active' %}
                                <span class="badge bg-success">Actief</span>
                            {% elif profile[4] == 'inactive' %}
                                <span class="badge bg-secondary">Inactief</span>
                            {% elif profile[4] == 'activated' %}
                                <span class="badge bg-info">Geactiveerd</span>
                            {% else %}
                                <span class="badge bg-warning">{{ profile[4] }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ profile[5]|datetime }}</small>
                        </td>
                        <td>
                            {% if profile[6] %}
                                <small class="text-success">{{ profile[6]|datetime }}</small>
                            {% else %}
                                <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if profile[7] %}
                                <small class="text-warning">{{ profile[7]|datetime }}</small>
                            {% else %}
                                <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('view_qr_code', activation_code=profile[1]) }}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   title="QR-code bekijken" 
                                   target="_blank">
                                    <i class="fas fa-qrcode"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-info" 
                                        onclick="showProfileDetails('{{ profile[1] }}')" 
                                        title="Details">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" 
                                        onclick="toggleProfileStatus('{{ profile[1] }}', '{{ profile[4] }}')" 
                                        title="Status wijzigen">
                                    <i class="fas fa-toggle-on"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteProfile('{{ profile[1] }}')" 
                                        title="Verwijderen">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Paginatie -->
        <nav aria-label="Profielen paginatie" class="mt-3">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <span class="page-link">Vorige</span>
                </li>
                <li class="page-item active">
                    <span class="page-link">1</span>
                </li>
                <li class="page-item disabled">
                    <span class="page-link">Volgende</span>
                </li>
            </ul>
        </nav>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
            <h5 class="text-muted">Geen eSIM profielen gevonden</h5>
            <p class="text-muted">Begin met het aanmaken van je eerste eSIM profiel.</p>
            <a href="{{ url_for('generate_profile') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle"></i> Eerste Profiel Aanmaken
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Profile Details Modal -->
<div class="modal fade" id="profileDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> Profiel Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="profileDetailsContent">
                <!-- Content wordt geladen via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Sluiten</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Filter functionaliteit
function filterProfiles() {
    const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;
    
    const table = document.getElementById('profilesTable');
    const rows = table.getElementsByTagName('tr');
    let visibleCount = 0;
    
    for (let i = 1; i < rows.length; i++) { // Skip header row
        const row = rows[i];
        const status = row.getAttribute('data-status').toLowerCase();
        const created = row.getAttribute('data-created');
        const text = row.textContent.toLowerCase();
        
        let showRow = true;
        
        // Status filter
        if (statusFilter && status !== statusFilter) {
            showRow = false;
        }
        
        // Search filter
        if (searchInput && !text.includes(searchInput)) {
            showRow = false;
        }
        
        // Date filter
        if (dateFilter && created !== dateFilter) {
            showRow = false;
        }
        
        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    }
    
    document.getElementById('profileCount').textContent = visibleCount;
}

function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('searchInput').value = '';
    document.getElementById('dateFilter').value = '';
    filterProfiles();
}

function refreshProfiles() {
    location.reload();
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show toast notification
        showToast('Activatiecode gekopieerd naar klembord!', 'success');
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        showToast('Fout bij kopiëren naar klembord', 'error');
    });
}

function showProfileDetails(activationCode) {
    // Hier zou je een API call kunnen maken om meer details op te halen
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Activatiecode:</h6>
                <p><code>${activationCode}</code></p>
                
                <h6>LPA URI:</h6>
                <p><code>LPA:1$sm-dp+.ciphercellvpn.com$${activationCode}</code></p>
            </div>
            <div class="col-md-6">
                <h6>QR-code:</h6>
                <img src="/qr_code/${activationCode}" class="img-fluid" alt="QR Code">
            </div>
        </div>
    `;
    
    document.getElementById('profileDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('profileDetailsModal')).show();
}

function toggleProfileStatus(activationCode, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    
    if (confirm(`Weet je zeker dat je de status wilt wijzigen naar '${newStatus}'?`)) {
        // Hier zou je een API call maken om de status te wijzigen
        showToast(`Status van ${activationCode} gewijzigd naar ${newStatus}`, 'info');
        setTimeout(() => location.reload(), 1000);
    }
}

function deleteProfile(activationCode) {
    if (confirm(`Weet je zeker dat je profiel ${activationCode} wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.`)) {
        // Hier zou je een API call maken om het profiel te verwijderen
        showToast(`Profiel ${activationCode} verwijderd`, 'warning');
        setTimeout(() => location.reload(), 1000);
    }
}

function showToast(message, type) {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Initialize filters on page load
document.addEventListener('DOMContentLoaded', function() {
    filterProfiles();
});
</script>
{% endblock %}