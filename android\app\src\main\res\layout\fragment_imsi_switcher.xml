<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="IMSI Switcher"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <TextView
                android:id="@+id/current_imsi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Current IMSI: "
                android:textSize="16sp"
                android:layout_marginBottom="16dp" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/mcc_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="MCC (Mobile Country Code)"
                    android:inputType="number" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/mnc_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="MNC (Mobile Network Code)"
                    android:inputType="number" />
            </com.google.android.material.textfield.TextInputLayout>

            <RadioGroup
                android:id="@+id/region_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <com.google.android.material.radiobutton.MaterialRadioButton
                    android:id="@+id/us_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="US"
                    android:checked="true" />

                <com.google.android.material.radiobutton.MaterialRadioButton
                    android:id="@+id/eu_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="EU" />

                <com.google.android.material.radiobutton.MaterialRadioButton
                    android:id="@+id/asia_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Asia" />
            </RadioGroup>

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/auto_rotate_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Auto-rotate IMSI"
                android:layout_marginBottom="16dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/rotate_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Rotate IMSI"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>