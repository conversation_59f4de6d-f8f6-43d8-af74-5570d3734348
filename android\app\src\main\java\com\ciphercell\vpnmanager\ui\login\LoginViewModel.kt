package com.ciphercell.vpnmanager.ui.login

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ciphercell.vpnmanager.network.LoginResponse
import com.ciphercell.vpnmanager.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * A sealed class to represent the result of a login operation.
 */
sealed class LoginResult {
    data class Success(val response: LoginResponse) : LoginResult()
    data class Error(val exception: Exception) : LoginResult()
    object Loading : LoginResult()
}

/**
 * ViewModel for the login screen.
 *
 * This ViewModel interacts with the [UserRepository] to perform user authentication
 * and exposes the login result to the UI.
 *
 * @param userRepository The repository for handling user data.
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val _loginResult = MutableLiveData<LoginResult>()
    val loginResult: LiveData<LoginResult> = _loginResult

    /**
     * Attempts to log in the user with the provided credentials.
     *
     * @param email The user's email.
     * @param password The user's password.
     */
    fun login(email: String, password: String) {
        // Set the state to Loading before starting the network call
        _loginResult.value = LoginResult.Loading

        // Launch a coroutine in the ViewModel's scope
        viewModelScope.launch {
            try {
                val response = userRepository.loginUser(email, password)
                _loginResult.postValue(LoginResult.Success(response))
            } catch (e: Exception) {
                _loginResult.postValue(LoginResult.Error(e))
            }
        }
    }
}
