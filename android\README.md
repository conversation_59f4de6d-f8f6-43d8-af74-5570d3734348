# CipherCell VPN Android App

A secure and feature-rich VPN client for Android that supports multiple VPN providers (WireGuard, NordVPN, Mullvad) with integrated IMSI changing capabilities.

## Features

### VPN Integration
- Support for multiple VPN providers:
  - WireGuard (with native integration)
  - NordVPN
  - Mullvad
- Automatic connection management
- Secure configuration storage
- Kill switch functionality
- DNS leak protection

### IMSI Management
- Dynamic IMSI generation
- Region-based IMSI allocation (US, EU, Asia)
- Automatic IMSI rotation
- IMSI history tracking

### Security Features
- Material Design 3 dark theme UI
- Secure configuration storage
- Auto-connect on boot option
- Connection status monitoring
- Detailed logging system

## Requirements

- Android 7.0 (API level 24) or higher
- Internet permission
- VPN service permission

## Building the App

### Prerequisites
1. Android Studio Arctic Fox (2021.3.1) or newer
2. JDK 11 or newer
3. Android SDK with API level 34

### Build Steps
1. Clone the repository
2. Open the project in Android Studio
3. Configure your signing keys in `local.properties`:
   ```properties
   KEYSTORE_FILE=path/to/keystore.jks
   KEYSTORE_PASSWORD=your_keystore_password
   KEY_ALIAS=your_key_alias
   KEY_PASSWORD=your_key_password
   ```
4. Build the project:
   ```bash
   ./gradlew assembleRelease
   ```

## Configuration

### VPN Provider Setup

#### WireGuard
1. Generate or import WireGuard keys
2. Configure server endpoints
3. Set up allowed IPs and DNS

#### NordVPN
1. Enter account credentials
2. Select preferred protocol
3. Choose server region

#### Mullvad
1. Enter account number
2. Select server location
3. Configure protocol settings

### IMSI Configuration
1. Select desired region (US, EU, Asia)
2. Configure auto-rotation interval
3. Set up IMSI generation parameters

## Security Recommendations

1. Always use the latest app version
2. Enable auto-updates
3. Use strong authentication credentials
4. Enable kill switch feature
5. Regularly rotate IMSI and VPN servers
6. Keep system and security patches up to date

## Troubleshooting

### Common Issues

1. VPN Connection Failures
   - Check internet connectivity
   - Verify VPN credentials
   - Ensure correct server configuration

2. IMSI Generation Issues
   - Verify region selection
   - Check for valid MCC/MNC combinations
   - Clear IMSI history if needed

3. Auto-rotation Problems
   - Check battery optimization settings
   - Verify background service permissions
   - Ensure proper wake lock handling

### Debug Logs

Enable debug logging in the app settings to gather detailed information for troubleshooting:
1. Go to Settings > Advanced
2. Enable Debug Logging
3. Reproduce the issue
4. Export logs for analysis

## Privacy Policy

This application:
- Does not collect personal information
- Does not track user activity
- Stores all configuration data locally
- Uses secure encryption for sensitive data
- Requires minimal permissions

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- WireGuard® is a registered trademark of Jason A. Donenfeld
- Material Design components by Google
- Open source libraries and their contributors