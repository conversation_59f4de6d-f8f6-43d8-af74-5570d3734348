package com.ciphercell.vpnmanager.di

import android.app.Application
import android.content.Context
import android.telephony.SubscriptionManager
import android.telephony.euicc.EuiccManager
import com.ciphercell.vpnmanager.esim.ESimManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideContext(application: Application): Context {
        return application.applicationContext
    }

    @Provides
    @Singleton
    fun provideEuiccManager(context: Context): EuiccManager {
        return context.getSystemService(Context.EUICC_SERVICE) as EuiccManager
    }

    @Provides
    @Singleton
    fun provideSubscriptionManager(context: Context): SubscriptionManager {
        return context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
    }

    @Provides
    @Singleton
    fun provideESimManager(
        context: Context,
        subscriptionManager: SubscriptionManager,
        euiccManager: EuiccManager
    ): ESimManager {
        return ESimManager(context, subscriptionManager, euiccManager)
    }
}
