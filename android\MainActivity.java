package com.ciphercell.vpnmanager;

import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.fragment.app.Fragment;

import com.ciphercell.vpnmanager.fragments.DashboardFragment;
import com.ciphercell.vpnmanager.fragments.IMSIProfileFragment;
import com.ciphercell.vpnmanager.fragments.SettingsFragment;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;

public class MainActivity extends AppCompatActivity {
    private BottomNavigationView bottomNav;
    private long backPressedTime;
    private Toast backToast;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        setContentView(R.layout.activity_main);

        initializeViews();
        setupBottomNavigation();
        
        // Load default fragment
        if (savedInstanceState == null) {
            loadFragment(new DashboardFragment());
        }
    }

    private void initializeViews() {
        bottomNav = findViewById(R.id.bottom_navigation);
    }

    private void setupBottomNavigation() {
        bottomNav.setOnItemSelectedListener(item -> {
            Fragment selectedFragment = null;
            int itemId = item.getItemId();
            
            if (itemId == R.id.nav_dashboard) {
                selectedFragment = new DashboardFragment();
            } else if (itemId == R.id.nav_imsi) {
                selectedFragment = new IMSIProfileFragment();
            } else if (itemId == R.id.nav_settings) {
                selectedFragment = new SettingsFragment();
            }
            
            if (selectedFragment != null) {
                return loadFragment(selectedFragment);
            }
            return false;
        });
    }
    
    private boolean loadFragment(Fragment fragment) {
        if (fragment != null) {
            getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit();
            return true;
        }
        return false;
    }
    
    @Override
    public void onBackPressed() {
        // If the current fragment is not the dashboard, go back to dashboard
        if (bottomNav.getSelectedItemId() != R.id.nav_dashboard) {
            bottomNav.setSelectedItemId(R.id.nav_dashboard);
        } else {
            // Double tap to exit
            if (backPressedTime + 2000 > System.currentTimeMillis()) {
                backToast.cancel();
                super.onBackPressed();
                return;
            } else {
                backToast = Toast.makeText(this, "Press back again to exit", Toast.LENGTH_SHORT);
                backToast.show();
            }
            backPressedTime = System.currentTimeMillis();
        }
    }
        
        // Set initial status
        updateStatus("Disconnected");
        updateIMSI(IMSIManager.getCurrentIMSI());
    }
    
    private void setupListeners() {
        connectButton.setOnClickListener(v -> connectVPN());
        disconnectButton.setOnClickListener(v -> disconnectVPN());
        rotateButton.setOnClickListener(v -> rotateConnection());
        
        autoRotateSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                startAutoRotation();
            } else {
                stopAutoRotation();
            }
        });
    }
    
    private void connectVPN() {
        String selectedVPN = getSelectedVPN();
        String selectedRegion = getSelectedRegion();
        
        VPNService.connect(selectedVPN, selectedRegion, new VPNCallback() {
            @Override
            public void onSuccess() {
                runOnUiThread(() -> {
                    updateStatus("Connected to " + selectedVPN);
                    Toast.makeText(MainActivity.this, "VPN Connected", Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    updateStatus("Connection failed");
                    Toast.makeText(MainActivity.this, error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
    
    private void disconnectVPN() {
        VPNService.disconnect(new VPNCallback() {
            @Override
            public void onSuccess() {
                runOnUiThread(() -> {
                    updateStatus("Disconnected");
                    Toast.makeText(MainActivity.this, "VPN Disconnected", Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
    
    private void rotateConnection() {
        String selectedVPN = getSelectedVPN();
        String selectedRegion = getSelectedRegion();
        
        IMSIManager.rotateIMSI(selectedRegion, new IMSICallback() {
            @Override
            public void onNewIMSI(String imsi) {
                runOnUiThread(() -> {
                    updateIMSI(imsi);
                    connectVPN(); // Reconnect with new IMSI
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
    
    private void startAutoRotation() {
        // Implementation for auto-rotation service
        AutoRotationService.start(this, getSelectedVPN(), getSelectedRegion());
        Toast.makeText(this, "Auto-rotation enabled", Toast.LENGTH_SHORT).show();
    }
    
    private void stopAutoRotation() {
        AutoRotationService.stop(this);
        Toast.makeText(this, "Auto-rotation disabled", Toast.LENGTH_SHORT).show();
    }
    
    private String getSelectedVPN() {
        int selectedId = vpnGroup.getCheckedRadioButtonId();
        if (selectedId == R.id.wireguard_radio) return "wireguard";
        if (selectedId == R.id.nordvpn_radio) return "nordvpn";
        if (selectedId == R.id.mullvad_radio) return "mullvad";
        return "wireguard"; // Default
    }
    
    private String getSelectedRegion() {
        int selectedId = regionGroup.getCheckedRadioButtonId();
        if (selectedId == R.id.us_radio) return "US";
        if (selectedId == R.id.eu_radio) return "EU";
        if (selectedId == R.id.asia_radio) return "ASIA";
        return "US"; // Default
    }
    
    private void updateStatus(String status) {
        statusText.setText(status);
    }
    
    private void updateIMSI(String imsi) {
        imsiText.setText("IMSI: " + imsi);
    }
}