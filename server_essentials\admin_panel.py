import os
import sys
import csv
import io
import re
import json
import secrets
import hashlib
import sqlite3
from datetime import datetime, timedelta
from functools import wraps
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, g
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from esim_builder import eSIMProfileBuilder, IMSIGenerator

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', os.urandom(24))
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=1)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

class User(UserMixin):
    def __init__(self, id, username, password_hash, role, last_login=None, failed_attempts=0, locked_until=None):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.role = role
        self.last_login = last_login
        self.failed_attempts = failed_attempts
        self.locked_until = locked_until

    def is_active(self):
        if self.locked_until:
            try:
                locked_until_dt = datetime.strptime(self.locked_until, '%Y-%m-%d %H:%M:%S')
                return locked_until_dt <= datetime.now()
            except (ValueError, TypeError):
                return True
        return True

    def get_id(self):
        return str(self.id)

    def is_admin(self):
        return self.role == 'admin'

@login_manager.user_loader
def load_user(user_id):
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    conn.close()
    if user:
        return User(
            id=user['id'],
            username=user['username'],
            password_hash=user['password'],
            role=user['role'],
            last_login=user['last_login'],
            failed_attempts=user['failed_attempts'],
            locked_until=user['locked_until']
        )
    return None

def get_db_connection():
    try:
        conn = sqlite3.connect('admin_panel.db', timeout=20)
        conn.row_factory = sqlite3.Row
        # Enable foreign key support
        conn.execute('PRAGMA foreign_keys = ON')
        return conn
    except sqlite3.Error as e:
        app.logger.error(f'Database connection error: {str(e)}')
        raise sqlite3.Error(f'Failed to connect to database: {str(e)}')

def init_db():
    print('Creating database connection...')
    conn = get_db_connection()
    
    print('Creating users table...')
    conn.execute('DROP TABLE IF EXISTS users')
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT NOT NULL,
            failed_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP,
            last_login TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print('Creating admin user...')
    password = generate_password_hash('xK9#mP2$vL5nQ8@jR3')
    conn.execute('INSERT INTO users (username, password, role) VALUES (?, ?, ?)',
                ('CipherAdmin2345fg', password, 'admin'))
    print('Admin user created successfully!')
    
    print('Creating other tables...')
    # Drop and recreate audit_log table
    conn.execute('DROP TABLE IF EXISTS audit_log')
    conn.execute('''
        CREATE TABLE audit_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            details TEXT,
            ip_address TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Create vpn_configs table
    conn.execute('DROP TABLE IF EXISTS vpn_configs')
    conn.execute('''
        CREATE TABLE vpn_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            server TEXT NOT NULL,
            port INTEGER NOT NULL,
            config TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create data_bundles table
    conn.execute('DROP TABLE IF EXISTS data_bundles')
    conn.execute('''
        CREATE TABLE data_bundles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            region TEXT NOT NULL,
            duration INTEGER NOT NULL,
            data_amount REAL NOT NULL,
            fair_use REAL NOT NULL,
            price REAL NOT NULL,
            active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Initialize data bundles with some default packages
    print('Creating default data bundles...')
    default_bundles = [
        ('Global Basic', 'Global', 30, 5, 1, 29.99, 1),
        ('Europe Premium', 'Europe', 30, 10, 2, 39.99, 1),
        ('Asia Plus', 'Asia', 30, 8, 1.5, 34.99, 1),
        ('Americas Unlimited', 'Americas', 30, 15, 3, 49.99, 1)
    ]
    conn.executemany(
        'INSERT INTO data_bundles (name, region, duration, data_amount, fair_use, price, active) VALUES (?, ?, ?, ?, ?, ?, ?)',
        default_bundles
    )
    print('Default data bundles created successfully!')
    
    # Add system audit log entry
    conn.execute('INSERT INTO audit_log (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)',
                 (None, 'system', 'Database initialized', '127.0.0.1'))
    
    conn.commit()
    conn.close()
    print('Database initialization completed!')

@app.route('/admin/logout')
@login_required
def admin_logout():
    user_id = current_user.id
    logout_user()
    log_audit(user_id, 'logout', 'User logged out')
    flash('You have been logged out successfully')
    return redirect(url_for('login'))

@app.route('/')
@login_required
def index():
    return redirect(url_for('admin_dashboard'))

@app.route('/admin/dashboard')
@login_required
def admin_dashboard():
    conn = get_db_connection()
    
    # Get audit logs for recent activity
    audit_logs = conn.execute(
        'SELECT audit_log.*, users.username FROM audit_log '
        'LEFT JOIN users ON audit_log.user_id = users.id '
        'ORDER BY timestamp DESC LIMIT 10'
    ).fetchall()
    
    # Get counts for dashboard stats
    user_count = conn.execute('SELECT COUNT(*) as count FROM users').fetchone()['count']
    esim_count = conn.execute('SELECT COUNT(*) as count FROM subscriptions WHERE status = "active"').fetchone()['count']
    
    # Format recent activities for the dashboard
    recent_activities = [{
        'time': log['timestamp'],
        'action': log['action'],
        'user': log['username'] or 'System',
        'status': 'Completed'
    } for log in audit_logs]
    
    conn.close()
    
    return render_template('admin/dashboard.html',
                           recent_activities=recent_activities,
                           user_count=user_count,
                           esim_count=esim_count,
                           system_load='Normal')

def log_audit(user_id, action, details=None):
    conn = get_db_connection()
    ip_address = request.remote_addr
    conn.execute('INSERT INTO audit_log (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)',
                (user_id, action, details, ip_address))
    conn.commit()
    conn.close()

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        conn = get_db_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()

        if user:
            # Check if account is locked
            locked_until = user['locked_until'] if 'locked_until' in user.keys() else None
            if locked_until and datetime.strptime(locked_until, '%Y-%m-%d %H:%M:%S') > datetime.now():
                flash('Account is locked. Please try again later.')
                return render_template('admin/login.html')

            if check_password_hash(user['password'], password):
                # Reset failed attempts on successful login
                conn.execute('UPDATE users SET failed_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP WHERE id = ?', 
                            (user['id'],))
                conn.commit()
                
                user_obj = User(
                    id=user['id'],
                    username=user['username'],
                    password_hash=user['password'],
                    role=user['role'],
                    last_login=user['last_login'],
                    failed_attempts=0,
                    locked_until=None
                )
                login_user(user_obj)
                
                log_audit(user['id'], 'login', 'Successful login')
                return redirect(url_for('index'))
            else:
                # Increment failed attempts
                failed_attempts = user['failed_attempts'] if 'failed_attempts' in user.keys() else 0
                failed_attempts += 1
                locked_until = None
                if failed_attempts >= 5:
                    locked_until = (datetime.now() + timedelta(minutes=30)).strftime('%Y-%m-%d %H:%M:%S')
                
                conn.execute('UPDATE users SET failed_attempts = ?, locked_until = ? WHERE id = ?',
                            (failed_attempts, locked_until, user['id']))
                conn.commit()
                
                log_audit(user['id'], 'login_failed', f'Failed attempt #{failed_attempts}')
                
                if locked_until:
                    flash('Too many failed attempts. Account locked for 30 minutes.')
                else:
                    flash('Invalid username or password')
        else:
            flash('Invalid username or password')
            
        conn.close()
    return render_template('admin/login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/api/esim/<int:esim_id>/revoke', methods=['POST'])
@login_required
def revoke_esim(esim_id):
    if not request.is_json:
        return jsonify({'error': 'Content-Type must be application/json'}), 400
        
    data = request.get_json()
    reason = data.get('reason')
    
    if not reason:
        return jsonify({'error': 'Revocation reason is required'}), 400
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Start transaction
        cursor.execute('BEGIN TRANSACTION')
        
        try:
            # Update eSIM status
            cursor.execute('''
                UPDATE esims
                SET status = 'revoked',
                    revocation_date = CURRENT_TIMESTAMP,
                    revocation_reason = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND status != 'revoked'
            ''', (reason, esim_id))
            
            if cursor.rowcount == 0:
                conn.rollback()
                return jsonify({'error': 'eSIM not found or already revoked'}), 404
            
            # Log the action
            cursor.execute('''
                INSERT INTO audit_log (user_id, action, details, ip_address)
                VALUES (?, ?, ?, ?)
            ''', (current_user.id, 'revoke_esim',
                  json.dumps({'esim_id': esim_id, 'reason': reason}),
                  request.remote_addr))
            
            conn.commit()
            return jsonify({'status': 'success', 'message': 'eSIM revoked successfully'})
            
        except Exception as e:
            conn.rollback()
            return jsonify({'error': 'Failed to revoke eSIM', 'details': str(e)}), 500
            
    except Exception as e:
        return jsonify({'error': 'Server error', 'details': str(e)}), 500
        
    finally:
        conn.close()

@app.route('/api/esim/<int:esim_id>/qr', methods=['GET'])
@login_required
def get_esim_qr(esim_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get eSIM details
        cursor.execute('''
            SELECT e.*, GROUP_CONCAT(i.imsi) as imsis
            FROM esims e
            LEFT JOIN imsi_numbers i ON e.id = i.esim_id
            WHERE e.id = ?
            GROUP BY e.id
        ''', (esim_id,))
        
        esim = cursor.fetchone()
        if not esim:
            return jsonify({'error': 'eSIM not found'}), 404
            
        # Update access count and last access date
        cursor.execute('''
            UPDATE esims
            SET access_count = access_count + 1,
                last_access_date = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (esim_id,))
        
        # Generate QR code (placeholder - implement actual QR code generation)
        import qrcode
        import base64
        from io import BytesIO
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(f"LPA:1${esim['iccid']}")
        qr.make(fit=True)
        
        img = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(f"LPA:1${esim['iccid']}")
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffered = BytesIO()
        img.save(buffered, format="PNG")
        qr_code = f"data:image/png;base64,{base64.b64encode(buffered.getvalue()).decode()}"
        
        conn.commit()
        
        return jsonify({
            'qr_code': qr_code,
            'encryption_version': esim['encryption_version'],
            'access_count': esim['access_count'],
            'last_access_date': esim['last_access_date']
        })
        
    except Exception as e:
        return jsonify({'error': 'Server error', 'details': str(e)}), 500
        
    finally:
        conn.close()

@app.route('/api/esim/<int:esim_id>', methods=['GET'])
@login_required
def get_esim(esim_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT e.*, GROUP_CONCAT(i.imsi) as imsis
            FROM esims e
            LEFT JOIN imsi_numbers i ON e.id = i.esim_id
            WHERE e.id = ?
            GROUP BY e.id
        ''', (esim_id,))
        
        esim = cursor.fetchone()
        if not esim:
            return jsonify({'error': 'eSIM not found'}), 404
            
        return jsonify(dict(esim))
        
    except Exception as e:
        return jsonify({'error': 'Server error', 'details': str(e)}), 500
        
    finally:
        conn.close()

@app.route('/api/esim/<int:esim_id>/update', methods=['POST'])
@login_required
def update_esim(esim_id):
    if not request.is_json:
        return jsonify({'error': 'Content-Type must be application/json'}), 400
        
    data = request.get_json()
    geo_restriction = data.get('geo_restriction')
    device_binding = data.get('device_binding')
    
    if geo_restriction is None or device_binding is None:
        return jsonify({'error': 'Missing required fields'}), 400
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Start transaction
        cursor.execute('BEGIN TRANSACTION')
        
        try:
            # Update eSIM settings
            cursor.execute('''
                UPDATE esims
                SET geo_restriction = ?,
                    device_binding = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (geo_restriction, device_binding, esim_id))
            
            if cursor.rowcount == 0:
                conn.rollback()
                return jsonify({'error': 'eSIM not found'}), 404
            
            # Log the action
            cursor.execute('''
                INSERT INTO audit_log (user_id, action, details, ip_address)
                VALUES (?, ?, ?, ?)
            ''', (current_user.id, 'update_esim',
                  json.dumps({
                      'esim_id': esim_id,
                      'geo_restriction': geo_restriction,
                      'device_binding': device_binding
                  }),
                  request.remote_addr))
            
            conn.commit()
            return jsonify({'status': 'success', 'message': 'eSIM updated successfully'})
            
        except Exception as e:
            conn.rollback()
            return jsonify({'error': 'Failed to update eSIM', 'details': str(e)}), 500
            
    except Exception as e:
        return jsonify({'error': 'Server error', 'details': str(e)}), 500
        
    finally:
        conn.close()

@app.route('/admin/esim')
@login_required
def admin_esim():
    conn = get_db_connection()
    
    # Get all eSIMs with their IMSI numbers
    esims = conn.execute(
        'SELECT e.*, GROUP_CONCAT(i.imsi) as imsi_list '
        'FROM esims e '
        'LEFT JOIN imsi_numbers i ON e.id = i.esim_id '
        'GROUP BY e.id '
        'ORDER BY e.created_at DESC'
    ).fetchall()
    
    # Convert Row objects to dictionaries and process IMSI lists
    esims_with_imsi = []
    for esim in esims:
        esim_dict = dict(esim)
        # Split IMSI list string into array or provide empty array if None
        esim_dict['imsi_numbers'] = esim_dict['imsi_list'].split(',') if esim_dict['imsi_list'] else []
        esims_with_imsi.append(esim_dict)
    
    conn.close()
    return render_template('admin/esim.html', esims=esims_with_imsi)

@app.route('/api/esim/generate', methods=['POST'])
@login_required
def generate_esim():
    try:
        data = request.get_json()
        if not data:
            raise ValueError("Request body is required")
            
        # Validate and get profile type
        profile_type = data.get('profile_type', 'standard')
        if profile_type not in ['standard', 'premium']:
            raise ValueError("Invalid profile type. Must be 'standard' or 'premium'")
            
        # Validate and get validity period
        try:
            validity_days = int(data.get('validity', 30))
            if validity_days < 1 or validity_days > 365:
                raise ValueError("Validity period must be between 1 and 365 days")
        except ValueError:
            raise ValueError("Invalid validity period")
        
        # Initialize generators with logging
        imsi_gen = IMSIGenerator()
        esim_builder = ESIMBuilder()
        
        # Generate IMSI numbers with validation
        imsi_count = 5 if profile_type == 'premium' else 1
        imsi_list = imsi_gen.generate_multiple_imsi(count=imsi_count)
        
        # Validate all generated IMSIs
        for imsi in imsi_list:
            valid, msg = imsi_gen.validate_imsi(imsi)
            if not valid:
                raise ValueError(f"Generated invalid IMSI: {msg}")
        
        # Generate unique ICCID with improved format
        iccid_prefix = '89' # Standard telecom prefix
        iccid_suffix = ''.join(secrets.choice('0123456789') for _ in range(18))
        iccid = iccid_prefix + iccid_suffix
        
        # Validate ICCID format
        if not re.match(r'^89\d{18}$', iccid):
            raise ValueError("Generated invalid ICCID format")
        
        # Calculate dates with timezone awareness
        created_date = datetime.now()
        expiry_date = created_date + timedelta(days=validity_days)
        
        # Create eSIM profile with enhanced security
        profile = esim_builder.create_esim_profile(
            activation_code=iccid,
            operator_name='CipherCell',
            imsi_list=imsi_list,
            profile_type=profile_type,
            encryption_password=secrets.token_urlsafe(32)
        )
        
        # Log the eSIM generation
        log_audit(
            current_user.id,
            'generate_esim',
            f"Generated {profile_type} eSIM with ICCID: {iccid}"
        )
        
        # Calculate security checksum
        security_data = f"{iccid}:{profile_type}:{','.join(imsi_list)}:{created_date.isoformat()}"
        security_checksum = hashlib.sha256(security_data.encode()).hexdigest()
        
        # Generate encryption key hash for storage
        encryption_key = secrets.token_urlsafe(32)
        key_hash = hashlib.sha256(encryption_key.encode()).hexdigest()
        
        # Save to database with transaction
        conn = get_db_connection()
        try:
            conn.execute('BEGIN TRANSACTION')
            
            # Insert eSIM record with enhanced security
            conn.execute('''
                INSERT INTO esims (
                    iccid, status, created_date, expiry_date, profile_type,
                    encryption_version, encryption_key_hash, security_checksum
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (iccid, 'inactive', created_date, expiry_date, profile_type,
                  '2.0', key_hash, security_checksum))
            
            # Get the created eSIM
            esim = conn.execute('SELECT * FROM esims WHERE iccid = ?', (iccid,)).fetchone()
            
            # Store IMSI numbers with validation
            for imsi in imsi_list:
                conn.execute('''
                    INSERT INTO imsi_numbers (esim_id, imsi) 
                    VALUES (?, ?)
                ''', (esim['id'], imsi))
            
            # Update audit log
            log_audit(
                current_user.id,
                'esim_creation',
                f"Created {profile_type} eSIM {iccid} with {len(imsi_list)} IMSIs"
            )
            
            conn.commit()
            
            # Get associated IMSI numbers
            imsi_records = conn.execute('''
                SELECT imsi 
                FROM imsi_numbers 
                WHERE esim_id = ? 
                ORDER BY created_at
            ''', (esim['id'],)).fetchall()
            
            # Prepare response data
            esim_dict = dict(esim)
            esim_dict['imsi_numbers'] = [record['imsi'] for record in imsi_records]
            
            # Remove sensitive data from response
            sensitive_fields = ['encryption_key_hash', 'security_checksum']
            for field in sensitive_fields:
                esim_dict.pop(field, None)
            
            return jsonify({
                'status': 'success',
                'message': 'eSIM created successfully',
                'esim': esim_dict,
                'profile_type': profile_type
            })
            
        except sqlite3.IntegrityError as e:
            conn.rollback()
            raise ValueError(f"Database integrity error: {str(e)}")
            
        except Exception as e:
            conn.rollback()
            raise
            
        finally:
            conn.close()
            
    except ValueError as e:
        return jsonify({
            'status': 'error',
            'error_type': 'validation_error',
            'message': str(e)
        }), 400
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error_type': 'system_error',
            'message': 'An unexpected error occurred'
        }), 500

@app.route('/admin/vps')
@login_required
def admin_vps():
    return render_template('admin/vps.html')

@app.route('/admin/users')
@login_required
def admin_users():
    if current_user.role != 'admin':
        flash('Access denied')
        return redirect(url_for('index'))
    conn = get_db_connection()
    users = conn.execute('SELECT id, username, role FROM users').fetchall()
    conn.close()
    return render_template('admin/users.html', users=users)

@app.route('/admin/vpn')
@login_required
def admin_vpn():
    return render_template('admin/vpn.html')

@app.route('/admin/databundles')
@login_required
def admin_databundles():
    return render_template('admin/databundles.html')

# Data Bundles API Routes
@app.route('/api/databundles', methods=['GET'])
@login_required
def get_databundles():
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        app.logger.info('Fetching data bundles from database')
        cur.execute('SELECT * FROM data_bundles ORDER BY name')
        rows = cur.fetchall()
        app.logger.info(f'Found {len(rows)} data bundles')
        bundles = []
        for row in rows:
            try:
                bundle = {
                    'id': row[0],
                    'name': row[1],
                    'region': row[2],
                    'duration': row[3],
                    'data_amount': float(row[4]),
                    'fair_use': float(row[5]),
                    'price': float(row[6]),
                    'active': bool(row[7])
                }
                bundles.append(bundle)
            except Exception as e:
                app.logger.error(f'Error processing bundle row {row}: {str(e)}')
        conn.close()
        return jsonify(bundles)
    except Exception as e:
        app.logger.error(f'Error in get_databundles: {str(e)}')
        return jsonify({'error': 'Failed to fetch data bundles', 'details': str(e)}), 500

@app.route('/api/databundles', methods=['POST'])
@login_required
def create_databundle():
    if not request.is_json:
        return jsonify({'error': 'Content-Type must be application/json'}), 400
    
    data = request.get_json()
    required_fields = ['name', 'region', 'duration', 'data_amount', 'fair_use', 'price']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'Missing required fields'}), 400

    conn = None
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(
            'INSERT INTO data_bundles (name, region, duration, data_amount, fair_use, price, active) VALUES (?, ?, ?, ?, ?, ?, 1)',
            (data['name'], data['region'], data['duration'], data['data_amount'], data['fair_use'], data['price'])
        )
        bundle_id = cur.lastrowid
        conn.commit()
        return jsonify({'id': bundle_id}), 201
    except sqlite3.Error as e:
        if conn:
            conn.rollback()
        app.logger.error(f'Database error in create_databundle: {str(e)}')
        return jsonify({'error': 'Failed to create data bundle', 'details': str(e)}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/databundles/<int:bundle_id>', methods=['PUT'])
@login_required
def update_databundle(bundle_id):
    if not request.is_json:
        return jsonify({'error': 'Content-Type must be application/json'}), 400
    
    data = request.get_json()
    required_fields = ['name', 'region', 'duration', 'data_amount', 'fair_use', 'price']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'Missing required fields'}), 400

    conn = None
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(
            'UPDATE data_bundles SET name = ?, region = ?, duration = ?, data_amount = ?, fair_use = ?, price = ? WHERE id = ?',
            (data['name'], data['region'], data['duration'], data['data_amount'], data['fair_use'], data['price'], bundle_id)
        )
        if cur.rowcount == 0:
            return jsonify({'error': 'Data bundle not found'}), 404
        conn.commit()
        return jsonify({'success': True})
    except sqlite3.Error as e:
        if conn:
            conn.rollback()
        app.logger.error(f'Database error in update_databundle: {str(e)}')
        return jsonify({'error': 'Failed to update data bundle', 'details': str(e)}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/databundles/<int:bundle_id>', methods=['DELETE'])
@login_required
def delete_databundle(bundle_id):
    conn = None
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute('DELETE FROM data_bundles WHERE id = ?', (bundle_id,))
        if cur.rowcount == 0:
            return jsonify({'error': 'Data bundle not found'}), 404
        conn.commit()
        return jsonify({'success': True})
    except sqlite3.Error as e:
        if conn:
            conn.rollback()
        app.logger.error(f'Database error in delete_databundle: {str(e)}')
        return jsonify({'error': 'Failed to delete data bundle', 'details': str(e)}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/databundles/import', methods=['POST'])
@login_required
def import_databundles():
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if not file.filename.endswith('.csv'):
        return jsonify({'error': 'File must be a CSV'}), 400

    conn = None
    try:
        # Read CSV file
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_reader = csv.DictReader(stream)
        
        conn = get_db_connection()
        cur = conn.cursor()
        imported_count = 0
        
        for row in csv_reader:
            try:
                cur.execute(
                    'INSERT INTO data_bundles (name, region, duration, data_amount, fair_use, price, active) VALUES (?, ?, ?, ?, ?, ?, 1)',
                    (row['name'], row['region'], int(row['duration']), 
                     float(row['data_amount']), float(row['fair_use']), float(row['price']))
                )
                imported_count += 1
            except (ValueError, KeyError) as e:
                app.logger.error(f'Error processing CSV row: {row}, Error: {str(e)}')
                return jsonify({'error': f'Invalid data in CSV: {str(e)}'}), 400
        
        conn.commit()
        return jsonify({'success': True, 'imported_count': imported_count})
    except csv.Error as e:
        app.logger.error(f'CSV parsing error: {str(e)}')
        return jsonify({'error': f'Failed to parse CSV file: {str(e)}'}), 400
    except sqlite3.Error as e:
        if conn:
            conn.rollback()
        app.logger.error(f'Database error in import_databundles: {str(e)}')
        return jsonify({'error': 'Failed to import data bundles', 'details': str(e)}), 500
    except Exception as e:
        if conn:
            conn.rollback()
        app.logger.error(f'Unexpected error in import_databundles: {str(e)}')
        return jsonify({'error': 'An unexpected error occurred', 'details': str(e)}), 500
    finally:
        if conn:
            conn.close()

# VPN Configuration Management
@app.route('/api/vpn/configs', methods=['GET'])
@login_required
def get_vpn_configs():
    conn = get_db_connection()
    configs = conn.execute('SELECT * FROM vpn_configs ORDER BY created_at DESC').fetchall()
    conn.close()
    return jsonify([dict(config) for config in configs])

@app.route('/api/vpn/config', methods=['POST'])
@login_required
def add_vpn_config():
    data = request.get_json()
    conn = get_db_connection()
    try:
        conn.execute('INSERT INTO vpn_configs (name, type, server, port, config) VALUES (?, ?, ?, ?, ?)',
                    (data['name'], data['type'], data['server'], data['port'], data['config']))
        conn.commit()
        log_audit(current_user.id, 'add_vpn_config', f"Added VPN config: {data['name']}")
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})
    finally:
        conn.close()

@app.route('/api/vpn/config/<int:config_id>', methods=['PUT', 'DELETE'])
@login_required
def manage_vpn_config(config_id):
    conn = get_db_connection()
    try:
        if request.method == 'DELETE':
            conn.execute('DELETE FROM vpn_configs WHERE id = ?', (config_id,))
            conn.commit()
            log_audit(current_user.id, 'delete_vpn_config', f"Deleted VPN config ID: {config_id}")
            return jsonify({'status': 'success'})
        else:
            data = request.get_json()
            conn.execute('UPDATE vpn_configs SET name = ?, type = ?, server = ?, port = ?, config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                        (data['name'], data['type'], data['server'], data['port'], data['config'], config_id))
            conn.commit()
            log_audit(current_user.id, 'update_vpn_config', f"Updated VPN config ID: {config_id}")
            return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})
    finally:
        conn.close()

# Data Bundle Management
@app.route('/api/bundles', methods=['GET'])
@login_required
def get_data_bundles():
    conn = get_db_connection()
    bundles = conn.execute('SELECT * FROM data_bundles ORDER BY created_at DESC').fetchall()
    conn.close()
    return jsonify([dict(bundle) for bundle in bundles])

@app.route('/api/bundle', methods=['POST'])
@login_required
def add_data_bundle():
    data = request.get_json()
    conn = get_db_connection()
    try:
        conn.execute('INSERT INTO data_bundles (name, data_amount, duration, price) VALUES (?, ?, ?, ?)',
                    (data['name'], data['data_amount'], data['duration'], data['price']))
        conn.commit()
        log_audit(current_user.id, 'add_data_bundle', f"Added data bundle: {data['name']}")
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})
    finally:
        conn.close()

@app.route('/api/bundle/<int:bundle_id>', methods=['PUT', 'DELETE'])
@login_required
def manage_data_bundle(bundle_id):
    conn = get_db_connection()
    try:
        if request.method == 'DELETE':
            conn.execute('DELETE FROM data_bundles WHERE id = ?', (bundle_id,))
            conn.commit()
            log_audit(current_user.id, 'delete_data_bundle', f"Deleted data bundle ID: {bundle_id}")
            return jsonify({'status': 'success'})
        else:
            data = request.get_json()
            conn.execute('UPDATE data_bundles SET name = ?, data_amount = ?, duration = ?, price = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                        (data['name'], data['data_amount'], data['duration'], data['price'], bundle_id))
            conn.commit()
            log_audit(current_user.id, 'update_data_bundle', f"Updated data bundle ID: {bundle_id}")
            return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})
    finally:
        conn.close()

# Subscription Management
@app.route('/api/subscriptions', methods=['GET'])
@login_required
def get_subscriptions():
    conn = get_db_connection()
    subscriptions = conn.execute('''
        SELECT s.*, u.username, b.name as bundle_name, v.name as vpn_name 
        FROM subscriptions s 
        JOIN users u ON s.user_id = u.id 
        JOIN data_bundles b ON s.bundle_id = b.id 
        JOIN vpn_configs v ON s.vpn_config_id = v.id 
        ORDER BY s.created_at DESC
    ''').fetchall()
    conn.close()
    return jsonify([dict(sub) for sub in subscriptions])

@app.route('/api/subscription', methods=['POST'])
@login_required
def add_subscription():
    data = request.get_json()
    conn = get_db_connection()
    try:
        conn.execute('''
            INSERT INTO subscriptions 
            (user_id, bundle_id, vpn_config_id, start_date, end_date) 
            VALUES (?, ?, ?, ?, ?)
        ''', (data['user_id'], data['bundle_id'], data['vpn_config_id'],
              data['start_date'], data['end_date']))
        conn.commit()
        log_audit(current_user.id, 'add_subscription', f"Added subscription for user ID: {data['user_id']}")
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})
    finally:
        conn.close()

def get_db():
    if not hasattr(g, 'db'):
        g.db = get_db_connection()
    return g.db

def close_db(e=None):
    db = g.pop('db', None)
    if db is not None:
        db.close()

app.teardown_appcontext(close_db)

if __name__ == '__main__':
    print('Initializing database...')
    init_db()
    print('Database initialized successfully!')
    app.run(debug=True)