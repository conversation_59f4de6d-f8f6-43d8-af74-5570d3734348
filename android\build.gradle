buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.48.1'
    }
}

// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' apply false
    id 'org.jetbrains.kotlin.android' apply false
    id 'com.google.dagger.hilt.android' apply false
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin' apply false
}