$publicKey = Get-Content "C:\Users\<USER>\.ssh\id_rsa.pub"
$password = "admin123"
$server = "*************"

# Alternative approach using expect-like script (not used)
<#
This would require an expect-like tool for PowerShell
spawn ssh root@$server
expect "password:"
send "$password\r"
expect "# "
send "mkdir -p ~/.ssh\r"
expect "# "
send "echo '$publicKey' >> ~/.ssh/authorized_keys\r"
expect "# "
send "chmod 700 ~/.ssh\r"
expect "# "
send "chmod 600 ~/.ssh/authorized_keys\r"
expect "# "
send "exit\r"
expect eof
#>

Write-Host "Setting up SSH key authentication..."
Write-Host "Server: $server"
Write-Host "Public key: $($publicKey.Substring(0,50))..."

# Try using OpenSSH client with password
$env:SSH_ASKPASS_REQUIRE = "never"
echo $password | ssh -o StrictHostKeyChecking=no -o PasswordAuthentication=yes root@$server "mkdir -p ~/.ssh && echo '$publicKey' >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys"