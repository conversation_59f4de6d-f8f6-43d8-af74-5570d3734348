package com.ciphercell.vpnmanager.fragments

import android.app.Activity
import android.content.Intent
import android.net.VpnService
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.ciphercell.vpnmanager.databinding.FragmentDashboardBinding
import com.ciphercell.vpnmanager.vpn.CipherVpnService
import com.ciphercell.vpnmanager.vpn.VpnManager
import com.wireguard.android.backend.Tunnel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class DashboardFragment : Fragment() {

    private var _binding: FragmentDashboardBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var vpnManager: VpnManager

    private val vpnPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            startVpn()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDashboardBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.connectButton.setOnClickListener {
            if (vpnManager.vpnState.value == Tunnel.State.UP) {
                stopVpn()
            } else {
                prepareAndStartVpn()
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            vpnManager.vpnState.collectLatest { state ->
                updateUi(state)
            }
        }
    }

    private fun updateUi(state: Tunnel.State) {
        when (state) {
            Tunnel.State.UP -> {
                binding.connectButton.text = "Disconnect"
                binding.statusText.text = "Status: Connected"
            }
            Tunnel.State.DOWN -> {
                binding.connectButton.text = "Connect"
                binding.statusText.text = "Status: Disconnected"
            }
            else -> {
                binding.connectButton.text = "Connecting..."
                binding.statusText.text = "Status: Connecting..."
            }
        }
    }

    private fun prepareAndStartVpn() {
        val intent = VpnService.prepare(requireContext())
        if (intent != null) {
            vpnPermissionLauncher.launch(intent)
        } else {
            startVpn()
        }
    }

    private fun startVpn() {
        val intent = Intent(requireContext(), CipherVpnService::class.java).apply {
            action = CipherVpnService.ACTION_CONNECT
            // THIS IS A SAMPLE CONFIG - REPLACE WITH REAL CONFIGURATION
            putExtra(CipherVpnService.EXTRA_CONFIG, SAMPLE_WIREGUARD_CONFIG)
        }
        requireActivity().startService(intent)
    }

    private fun stopVpn() {
        val intent = Intent(requireContext(), CipherVpnService::class.java).apply {
            action = CipherVpnService.ACTION_DISCONNECT
        }
        requireActivity().startService(intent)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        // IMPORTANT: This is a placeholder configuration for development purposes.
        // In a real application, you would fetch this from a secure source.
        private const val SAMPLE_WIREGUARD_CONFIG = """
[Interface]
PrivateKey = <YOUR_PRIVATE_KEY>
Address = ********/32
DNS = *******

[Peer]
PublicKey = <YOUR_SERVER_PUBLIC_KEY>
AllowedIPs = 0.0.0.0/0
Endpoint = <YOUR_SERVER_IP>:51820
"""
    }
}
